# Next.js 15 Async Params Fix

## Issue
Next.js 15 introduced a breaking change where route parameters (`params`) are now returned as a Promise and must be unwrapped using `React.use()` before accessing properties.

## Error Message
```
A param property was accessed directly with `params.id`. `params` is now a Promise and should be unwrapped with `React.use()` before accessing properties of the underlying params object.
```

## Solution Applied

### Before (Next.js 14 style):
```tsx
interface TemplatePreviewPageProps {
  params: {
    id: string
  }
}

export default function TemplatePreviewPage({ params }: TemplatePreviewPageProps) {
  const template = getTemplateById(params.id) // ❌ Direct access
}
```

### After (Next.js 15 compatible):
```tsx
import { use } from "react"

interface TemplatePreviewPageProps {
  params: Promise<{
    id: string
  }>
}

export default function TemplatePreviewPage({ params }: TemplatePreviewPageProps) {
  const resolvedParams = use(params) // ✅ Unwrap the Promise
  const template = getTemplateById(resolvedParams.id) // ✅ Access resolved params
}
```

## Changes Made

### 1. Import `use` Hook
```tsx
import { useState, use } from "react"
```

### 2. Update Interface
```tsx
interface TemplatePreviewPageProps {
  params: Promise<{
    id: string
  }>
}
```

### 3. Unwrap Params
```tsx
const resolvedParams = use(params)
const template = getTemplateById(resolvedParams.id)
```

## Why This Change?

### Next.js 15 Motivation
- **Better Performance**: Allows for more efficient server-side rendering
- **Streaming Support**: Enables better streaming and partial hydration
- **Type Safety**: Clearer distinction between sync and async operations
- **Future-Proofing**: Prepares for upcoming React features

### Migration Path
- Next.js 15 currently supports both patterns for migration
- Direct access shows warnings but still works
- Future versions will require the new pattern

## Files Updated
- `app/ads/templates/[id]/preview/page.tsx`

## Testing
- ✅ Page loads correctly
- ✅ Template ID is properly resolved
- ✅ No console warnings
- ✅ All functionality preserved

## Best Practices

### For New Code
Always use the new async pattern:
```tsx
export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  // Use id...
}
```

### For Migration
1. Add `use` import
2. Update interface to use `Promise<>`
3. Unwrap params with `use()`
4. Update all param property access

This ensures compatibility with Next.js 15+ and eliminates console warnings.
