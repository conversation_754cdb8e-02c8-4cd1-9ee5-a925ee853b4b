"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import AIOrbWidget from "./ai-orb-widget"

interface TestAgentModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  agentName: string
}

export function TestAgentModal({ open, onOpenChange, agentName }: TestAgentModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] bg-neutral-900 border-neutral-800 p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-white">Test Agent: {agentName}</DialogTitle>
          <DialogDescription className="text-neutral-400">
            Interact with your AI agent in real-time. Enable your microphone to speak.
          </DialogDescription>
        </DialogHeader>
        <div className="p-6 pt-4">
          <AIOrbWidget enableMicInput={true} simulateSpeaker={true} />
        </div>
      </DialogContent>
    </Dialog>
  )
}
