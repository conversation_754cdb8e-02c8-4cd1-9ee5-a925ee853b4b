"use client"

import { useState } from "react"
import { Monitor, Smartphone, Instagram, Facebook, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface AdTemplate {
  id: number
  title: string
  category: string
  creative: {
    media_requirements: {
      image_specs: string[]
    }
  }
}

interface CampaignPreviewProps {
  template: AdTemplate
  customData: {
    headline: string
    description: string
    cta: string
    websiteUrl: string
    campaignName: string
  }
  className?: string
}

type PreviewPlatform = "facebook-feed" | "facebook-mobile" | "instagram-feed" | "instagram-stories"

export function CampaignPreview({ template, customData, className = "" }: CampaignPreviewProps) {
  const [selectedPlatform, setSelectedPlatform] = useState<PreviewPlatform>("facebook-feed")

  const user = {
    company_name: "Miller & Sons",
  }

  const platforms = [
    {
      id: "facebook-feed" as PreviewPlatform,
      name: "Facebook Feed",
      icon: Facebook,
      description: "Desktop News Feed",
    },
    {
      id: "facebook-mobile" as PreviewPlatform,
      name: "Facebook Mobile",
      icon: Smartphone,
      description: "Mobile News Feed",
    },
    {
      id: "instagram-feed" as PreviewPlatform,
      name: "Instagram Feed",
      icon: Instagram,
      description: "Instagram Posts",
    },
    {
      id: "instagram-stories" as PreviewPlatform,
      name: "Instagram Stories",
      icon: Monitor,
      description: "Instagram Stories",
    },
  ]

  const renderFacebookFeedPreview = () => (
    <div className="bg-white text-black max-w-md mx-auto shadow-lg rounded-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center space-x-3 p-4 border-b border-gray-200">
        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
          {user?.company_name?.charAt(0) || "B"}
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <p className="font-semibold text-sm">{user?.company_name || "Your Business"}</p>
            <span className="text-blue-500 text-xs">✓</span>
          </div>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>Sponsored</span>
            <span>•</span>
            <span>🌍</span>
          </div>
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          <span className="text-lg">⋯</span>
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-sm mb-3">{customData.description}</p>
      </div>

      {/* Image */}
      <div className="bg-blue-gradient h-64 flex items-center justify-center opacity-80">
        <div className="text-center text-gray-600">
          <Eye size={48} className="mx-auto mb-2 opacity-50" />
          <p className="text-sm">Campaign Image</p>
          <p className="text-xs opacity-75">1200x628</p>
        </div>
      </div>

      {/* Link Preview */}
      <div className="border-t bg-gray-50 p-4">
        <div className="flex items-start space-x-3">
          <div className="w-16 h-16 bg-gray-200 flex items-center justify-center text-xs text-gray-500 rounded">
            LOGO
          </div>
          <div className="flex-1">
            <p className="font-semibold text-sm text-gray-900">{customData.headline}</p>
            <p className="text-xs text-gray-500 mb-2">{customData.websiteUrl}</p>
            <Button className="bg-blue-gradient-hover text-white px-4 py-2 text-sm font-medium w-full">
              {customData.cta.replace("_", " ")}
            </Button>
          </div>
        </div>
      </div>

      {/* Engagement */}
      <div className="border-t p-4">
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center space-x-4">
            <span>👍 ❤️ 😮 12</span>
          </div>
          <span>2 comments • 1 share</span>
        </div>

        <div className="flex items-center justify-between border-t pt-2">
          <button className="flex-1 flex items-center justify-center space-x-2 py-2 text-gray-600 hover:bg-gray-50 rounded">
            <span>👍</span>
            <span className="text-sm font-medium">Like</span>
          </button>
          <button className="flex-1 flex items-center justify-center space-x-2 py-2 text-gray-600 hover:bg-gray-50 rounded">
            <span>💬</span>
            <span className="text-sm font-medium">Comment</span>
          </button>
          <button className="flex-1 flex items-center justify-center space-x-2 py-2 text-gray-600 hover:bg-gray-50 rounded">
            <span>↗️</span>
            <span className="text-sm font-medium">Share</span>
          </button>
        </div>
      </div>
    </div>
  )

  const renderPreview = () => {
    switch (selectedPlatform) {
      case "facebook-feed":
        return renderFacebookFeedPreview()
      default:
        return renderFacebookFeedPreview()
    }
  }

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Platform Selector */}
      <div className="mb-4">
        <h3 className="text-lg font-medium text-white mb-3">Preview Platforms</h3>
        <div className="grid grid-cols-4 gap-2">
          {platforms.map((platform) => {
            const IconComponent = platform.icon
            return (
              <button
                key={platform.id}
                onClick={() => setSelectedPlatform(platform.id)}
                className={`p-2 border text-center transition-all rounded-lg ${
                  selectedPlatform === platform.id
                    ? "border-blue-500 bg-blue-500/10 text-blue-400 glow-blue"
                    : "border-neutral-700 text-neutral-400 hover:border-blue-500/40 hover:bg-blue-500/5"
                }`}
              >
                <IconComponent size={16} className="mx-auto mb-1" />
                <p className="font-medium text-xs leading-tight">{platform.name}</p>
                <p className="text-xs opacity-75 leading-tight">{platform.description}</p>
              </button>
            )
          })}
        </div>
      </div>

      {/* Preview */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium text-white">Live Preview</h3>
          <div className="flex items-center space-x-2 text-sm text-neutral-400">
            <Eye size={16} />
            <span>Real-time preview</span>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <Card className="p-4 bg-neutral-900 border-neutral-800 mb-3">
            <CardContent className="p-0">{renderPreview()}</CardContent>
          </Card>

          {/* Preview Notes */}
          <Card className="bg-blue-500/10 border-blue-500/20">
            <CardContent className="p-3">
              <h4 className="font-medium text-white mb-2 text-sm">Preview Notes</h4>
              <ul className="text-xs text-blue-300 space-y-1">
                <li>• Images will be automatically optimized for each platform</li>
                <li>• Text may be truncated on mobile devices</li>
                <li>• Actual engagement metrics will vary based on audience</li>
                <li>• Colors may appear differently on various devices</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
