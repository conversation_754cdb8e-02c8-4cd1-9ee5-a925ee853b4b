"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { User, CreditCard, Phone, Calendar, Zap, SettingsIcon, Plus, Trash2, ExternalLink } from "lucide-react"
import { Navigation } from "@/components/navigation"

export default function SettingsPage() {
  const [notifications, setNotifications] = useState({
    emailReports: true,
    smsAlerts: false,
    appointmentReminders: true,
  })

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="settings" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-medium text-white">Settings</h1>
          <p className="text-neutral-400 mt-1 text-sm">Manage your account and integration settings</p>
        </div>

        <div className="max-w-4xl space-y-6">
          {/* User Profile */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <User className="h-4 w-4 text-white" />
                </div>
                User Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="business-name" className="text-sm font-medium text-white">
                    Business Name
                  </Label>
                  <Input
                    id="business-name"
                    defaultValue="Miller & Sons Roofing"
                    className="bg-neutral-800 border-neutral-700 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="owner-name" className="text-sm font-medium text-white">
                    Owner Name
                  </Label>
                  <Input
                    id="owner-name"
                    defaultValue="Frank Miller"
                    className="bg-neutral-800 border-neutral-700 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-sm font-medium text-white">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    defaultValue="<EMAIL>"
                    className="bg-neutral-800 border-neutral-700 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="phone" className="text-sm font-medium text-white">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    defaultValue="(*************"
                    className="bg-neutral-800 border-neutral-700 text-white"
                  />
                </div>
              </div>
              <Button className="bg-blue-gradient-hover text-white shadow-lg">Save Changes</Button>
            </CardContent>
          </Card>

          {/* Billing */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <CreditCard className="h-4 w-4 text-white" />
                </div>
                Billing & Subscription
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center p-4 bg-neutral-800 rounded-lg">
                <div>
                  <h4 className="font-medium text-white">Professional Plan</h4>
                  <p className="text-sm text-neutral-400">$297/month • Unlimited agents & campaigns</p>
                </div>
                <Badge className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border-blue-500/20">Active</Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-white">Payment Method</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-neutral-400">•••• •••• •••• 4242</span>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    >
                      Update
                    </Button>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-white">Next Billing Date</Label>
                  <p className="text-sm text-neutral-400 mt-1">August 19, 2025</p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                >
                  View Billing History
                </Button>
                <Button
                  variant="outline"
                  className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                >
                  Change Plan
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Phone Numbers */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <Phone className="h-4 w-4 text-white" />
                </div>
                Phone Numbers
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border border-neutral-800 rounded-lg">
                  <div>
                    <p className="font-medium text-white">(*************</p>
                    <p className="text-sm text-neutral-400">Main Office Line</p>
                  </div>
                  <div className="flex gap-2">
                    <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Active</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-neutral-400 hover:text-white hover:bg-neutral-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border border-neutral-800 rounded-lg">
                  <div>
                    <p className="font-medium text-white">(555) 987-6543</p>
                    <p className="text-sm text-neutral-400">Campaign Line</p>
                  </div>
                  <div className="flex gap-2">
                    <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Active</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-neutral-400 hover:text-white hover:bg-neutral-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                className="w-full border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add New Number
              </Button>
            </CardContent>
          </Card>

          {/* Integrations */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <Zap className="h-4 w-4 text-white" />
                </div>
                Integrations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between p-4 border border-neutral-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-6 w-6 text-neutral-400" />
                    <div>
                      <h4 className="font-medium text-white">Google Calendar</h4>
                      <p className="text-sm text-neutral-400"><NAME_EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border-blue-500/20">
                      Connected
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border border-neutral-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Zap className="h-6 w-6 text-neutral-400" />
                    <div>
                      <h4 className="font-medium text-white">Zapier</h4>
                      <p className="text-sm text-neutral-400">Connect to 5,000+ apps</p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  >
                    Connect
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <SettingsIcon className="h-4 w-4 text-white" />
                </div>
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-reports" className="text-sm font-medium text-white">
                      Daily Email Reports
                    </Label>
                    <p className="text-sm text-neutral-400">Receive daily summaries of agent performance</p>
                  </div>
                  <Switch
                    id="email-reports"
                    checked={notifications.emailReports}
                    onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, emailReports: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sms-alerts" className="text-sm font-medium text-white">
                      SMS Alerts
                    </Label>
                    <p className="text-sm text-neutral-400">Get text alerts for urgent issues</p>
                  </div>
                  <Switch
                    id="sms-alerts"
                    checked={notifications.smsAlerts}
                    onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, smsAlerts: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="appointment-reminders" className="text-sm font-medium text-white">
                      Appointment Reminders
                    </Label>
                    <p className="text-sm text-neutral-400">Email reminders for upcoming appointments</p>
                  </div>
                  <Switch
                    id="appointment-reminders"
                    checked={notifications.appointmentReminders}
                    onCheckedChange={(checked) =>
                      setNotifications((prev) => ({ ...prev, appointmentReminders: checked }))
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
