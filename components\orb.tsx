"use client"

import { useRef, useMemo } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { Sphere } from "@react-three/drei"
import * as THREE from "three"

export type Speaker = 0 | 1 | 2 // 0 = idle/thinking, 1 = user speaking, 2 = agent speaking

interface OrbProps {
  speaker: Speaker
  volume: number // 0..1
}

export default function Orb({ speaker, volume }: OrbProps) {
  const mesh = useRef<THREE.Mesh>(null!)
  const outerMesh = useRef<THREE.Mesh>(null!)
  const fogMesh = useRef<THREE.Mesh>(null!)

  // Create custom gradient material (main orb)
  const gradientMaterial = useMemo(() => {
    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vPosition;
      void main() {
        vUv = uv;
        vPosition = position;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform float time;
      uniform float speaker;
      uniform float volume;
      varying vec2 vUv;
      varying vec3 vPosition;

      // Noise function for organic flow
      float noise(vec3 p) {
        return sin(p.x * 10.0) * sin(p.y * 10.0) * sin(p.z * 10.0) * 0.1;
      }

      void main() {
        vec2 center = vec2(0.5, 0.5);
        float dist = distance(vUv, center);
        
        // Define your color palette
        vec3 color1 = vec3(0.678, 0.463, 0.847); // #AD1FBF - Magenta
        vec3 color2 = vec3(0.443, 0.463, 0.851); // #7176D9 - Purple
        vec3 color3 = vec3(0.341, 0.682, 0.949); // #57AAF2 - Light Blue
        vec3 color4 = vec3(0.153, 0.804, 0.949); // #27CDF2 - Cyan

        vec3 finalMix;
        float currentFlowSpeed;
        float currentIntensity;
        float currentAlpha;

        if (speaker < 0.5) { // Thinking/Idle state (speaker 0)
          currentFlowSpeed = 0.5 + volume * 0.2; 
          currentIntensity = 0.5 + volume * 0.3; // Adjusted for dullness
          currentAlpha = 0.5 + volume * 0.1; // Adjusted for dullness
          finalMix = mix(color2, color3, sin(time * 0.5 + vPosition.x * 0.5) * 0.5 + 0.5); 
        } else if (speaker < 1.5) { // User Speaking (speaker 1)
          currentFlowSpeed = 1.0 + volume * 0.4; 
          currentIntensity = 1.0 + volume * 0.5; // Adjusted for brightness
          currentAlpha = 0.9 + volume * 0.05; // Adjusted for brightness
          finalMix = mix(color1, color2, sin(time * 0.8 + vPosition.y * 0.7) * 0.5 + 0.5); 
        } else { // Agent Speaking (speaker 2)
          currentFlowSpeed = 1.2 + volume * 0.6; 
          currentIntensity = 1.1 + volume * 0.6; // Adjusted for brightness
          currentAlpha = 0.95 + volume * 0.02; // Adjusted for brightness
          finalMix = mix(color3, color4, sin(time * 1.0 + vPosition.z * 0.9) * 0.5 + 0.5); 
        }
        
        // Create flowing coordinates based on currentFlowSpeed
        vec3 flowPos = vPosition + vec3(time * 0.3 * currentFlowSpeed, time * 0.2 * currentFlowSpeed, time * 0.4 * currentFlowSpeed);
        
        // Multiple noise layers for organic flow
        float flow1 = sin(flowPos.x * 3.0 + time * currentFlowSpeed) * cos(flowPos.y * 2.0 + time * 0.7 * currentFlowSpeed);
        float flow2 = sin(flowPos.y * 4.0 + time * 1.3 * currentFlowSpeed) * cos(flowPos.z * 3.0 + time * 0.5 * currentFlowSpeed);
        float flow3 = sin(flowPos.z * 2.0 + time * 0.8 * currentFlowSpeed) * cos(flowPos.x * 5.0 + time * 1.1 * currentFlowSpeed);
        
        // Re-mix based on zones for more complex patterns
        float zone1 = (flow1 + 1.0) * 0.5;
        float zone2 = (flow2 + 1.0) * 0.5;
        float zone3 = (flow3 + 1.0) * 0.5;
        
        finalMix = mix(finalMix, mix(mix(color1, color2, zone1), mix(color3, color4, zone2), zone3), 0.5);
        
        finalMix *= currentIntensity;
        
        // Create depth with distance-based mixing
        float depthFactor = 1.0 - dist * 0.3;
        finalMix *= depthFactor;
        
        // Soft edge falloff
        float alpha = 1.0 - smoothstep(0.2, 0.8, dist); 
        alpha *= currentAlpha; 
        
        gl_FragColor = vec4(finalMix, alpha);
      }
    `

    return new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        time: { value: 0 },
        speaker: { value: speaker },
        volume: { value: volume },
      },
      transparent: true,
      blending: THREE.AdditiveBlending,
    })
  }, [])

  // Outer glow material
  const glowMaterial = useMemo(() => {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform float time;
      uniform float speaker;
      uniform float volume;
      varying vec2 vUv;

      void main() {
        vec2 center = vec2(0.5, 0.5);
        float dist = distance(vUv, center);
        
        // Soft outer glow, more subtle
        float glow = 1.0 - smoothstep(0.0, 1.0, dist); 
        glow = pow(glow, 4.0); 
        
        // Color based on speaker state
        vec3 glowColor;
        if (speaker < 0.5) {
          glowColor = vec3(0.5, 0.7, 0.9); 
        } else if (speaker < 1.5) {
          glowColor = vec3(0.8, 0.4, 0.9); 
        } else {
          glowColor = vec3(0.3, 0.8, 0.95); 
        }
        
        float alpha;
        if (speaker < 0.5) { // Thinking
          alpha = glow * (0.15 + volume * 0.05); // Adjusted for dullness
        } else { // User or Agent Speaking
          alpha = glow * (0.4 + volume * 0.1); // Adjusted for brightness
        }
        gl_FragColor = vec4(glowColor, alpha);
      }
    `

    return new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        time: { value: 0 },
        speaker: { value: speaker },
        volume: { value: volume },
      },
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide,
    })
  }, [])

  // Glowing Fog Material
  const fogMaterial = useMemo(() => {
    const vertexShader = `
      varying vec3 vNormal;
      void main() {
        vNormal = normalMatrix * normal;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform float time;
      uniform float speaker;
      uniform float volume;
      varying vec3 vNormal;

      void main() {
        // Base color for the fog, slightly desaturated from the main palette
        vec3 fogColor;
        if (speaker < 0.5) { // Thinking
          fogColor = vec3(0.4, 0.5, 0.7); 
        } else if (speaker < 1.5) { // User
          fogColor = vec3(0.6, 0.3, 0.7); 
        } else { // Agent
          fogColor = vec3(0.2, 0.6, 0.7); 
        }

        // Add subtle noise for organic look
        float noiseVal = sin(vNormal.x * 5.0 + time * 0.1) * cos(vNormal.y * 5.0 + time * 0.15) * 0.1;
        fogColor += noiseVal * 0.1;

        float alpha;
        if (speaker < 0.5) { // Thinking
          alpha = 0.02 + volume * 0.03; // Adjusted for dullness
        } else { // User or Agent Speaking
          alpha = 0.1 + volume * 0.08; // Adjusted for brightness
        }
        alpha *= (1.0 - length(vNormal)); 
        alpha = pow(alpha, 1.5); 

        gl_FragColor = vec4(fogColor, alpha);
      }
    `

    return new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        time: { value: 0 },
        speaker: { value: speaker },
        volume: { value: volume },
      },
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide,
      depthWrite: false,
    })
  }, [])

  useFrame((state) => {
    if (!mesh.current || !outerMesh.current || !fogMesh.current) return

    const time = state.clock.elapsedTime

    // Update uniforms for all materials
    gradientMaterial.uniforms.time.value = time
    gradientMaterial.uniforms.speaker.value = speaker
    gradientMaterial.uniforms.volume.value = volume

    glowMaterial.uniforms.time.value = time
    glowMaterial.uniforms.speaker.value = speaker
    glowMaterial.uniforms.volume.value = volume

    fogMaterial.uniforms.time.value = time
    fogMaterial.uniforms.speaker.value = speaker
    fogMaterial.uniforms.volume.value = volume

    // Scale with volume - larger base size, less reactive
    const s = 1.0 + volume * 0.15
    mesh.current.scale.set(s, s, s)

    // Outer glow scales proportionally - larger base size, less reactive
    const outerS = 1.03 + volume * 0.07
    outerMesh.current.scale.set(outerS, outerS, outerS)

    // Fog scales slightly with volume, but mostly static
    const fogS = 1.0 + volume * 0.05
    fogMesh.current.scale.set(fogS, fogS, fogS)

    // Gentle rotation
    mesh.current.rotation.y += 0.005
    outerMesh.current.rotation.y -= 0.003
    fogMesh.current.rotation.y -= 0.001 // Fog now rotates in the opposite direction
  })

  return (
    <group>
      {/* New: Glowing Fog Sphere - outermost layer */}
      <Sphere ref={fogMesh} args={[2.0, 32, 32]} material={fogMaterial} />
      {/* Outer glow sphere */}
      <Sphere ref={outerMesh} args={[1.4985, 32, 32]} material={glowMaterial} />
      {/* Main gradient sphere */}
      <Sphere ref={mesh} args={[1.458, 64, 64]} material={gradientMaterial} />
    </group>
  )
}
