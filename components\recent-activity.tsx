import { Calendar, Phone, UserPlus, CheckCircle, Clock } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "appointment",
    icon: Calendar,
    title: "New appointment booked for 'Roof Repair'",
    subtitle: "via Reactivation Campaign",
    time: "2 minutes ago",
    status: "success",
  },
  {
    id: 2,
    type: "transfer",
    icon: Phone,
    title: "Live transfer from 'Main Office Line'",
    subtitle: "to Sales Rep John",
    time: "15 minutes ago",
    status: "info",
  },
  {
    id: 3,
    type: "lead",
    icon: UserPlus,
    title: "New Lead 'Jane Doe' called",
    subtitle: "by Speed-to-Lead Agent",
    time: "32 minutes ago",
    status: "success",
  },
  {
    id: 4,
    type: "campaign",
    icon: CheckCircle,
    title: "Reactivation Campaign completed",
    subtitle: "1,500/5,000 leads called",
    time: "1 hour ago",
    status: "completed",
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "success":
      return "text-blue-400 bg-blue-500/10"
    case "info":
      return "text-blue-400 bg-blue-500/10"
    case "completed":
      return "text-neutral-400 bg-neutral-500/10"
    default:
      return "text-neutral-400 bg-neutral-500/10"
  }
}

export function RecentActivity() {
  return (
    <div className="space-y-4">
      {activities.map((activity) => {
        const Icon = activity.icon
        return (
          <div key={activity.id} className="flex items-start space-x-3">
            <div className={`p-2 rounded-full ${getStatusColor(activity.status)}`}>
              <Icon className="h-3 w-3" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white">{activity.title}</p>
              <p className="text-sm text-neutral-400">{activity.subtitle}</p>
              <div className="flex items-center mt-1">
                <Clock className="h-3 w-3 text-neutral-500 mr-1" />
                <span className="text-xs text-neutral-500">{activity.time}</span>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
