"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Plus, Megaphone, Play, Pause, BarChart3, Eye } from "lucide-react"
import { Navigation } from "@/components/navigation"

const adCampaigns = [
  {
    id: 1,
    name: "Spring House Washing Promo",
    platform: "Facebook Ads",
    status: "active",
    budget: 500,
    spent: 342,
    impressions: 12500,
    clicks: 234,
    leads: 18,
    startDate: "2025-07-01",
    endDate: "2025-07-31",
  },
  {
    id: 2,
    name: "Roof Cleaning Before/After",
    platform: "Google Ads",
    status: "active",
    budget: 300,
    spent: 156,
    impressions: 8900,
    clicks: 167,
    leads: 12,
    startDate: "2025-07-10",
    endDate: "2025-07-25",
  },
  {
    id: 3,
    name: "Commercial Fleet Washing",
    platform: "LinkedIn Ads",
    status: "paused",
    budget: 750,
    spent: 423,
    impressions: 5600,
    clicks: 89,
    leads: 7,
    startDate: "2025-06-15",
    endDate: "2025-07-15",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border-blue-500/20">Active</Badge>
    case "paused":
      return (
        <Badge className="bg-yellow-500/10 text-yellow-400 hover:bg-yellow-500/20 border-yellow-500/20">Paused</Badge>
      )
    case "completed":
      return (
        <Badge className="bg-green-500/10 text-green-400 hover:bg-green-500/20 border-green-500/20">Completed</Badge>
      )
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
  }
}

export default function AdCampaignsPage() {
  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="ads-campaigns" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Ad Campaigns</h1>
            <p className="text-neutral-400 mt-1 text-sm">Manage your advertising campaigns across platforms</p>
          </div>
          <Button className="flex items-center gap-2 bg-blue-gradient-hover text-white shadow-lg">
            <Plus className="h-4 w-4" />
            New Campaign
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Active Campaigns</p>
                  <p className="text-2xl font-medium text-white">
                    {adCampaigns.filter((c) => c.status === "active").length}
                  </p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Megaphone className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Spent</p>
                  <p className="text-2xl font-medium text-white">${adCampaigns.reduce((sum, c) => sum + c.spent, 0)}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <span className="text-2xl">💰</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Leads</p>
                  <p className="text-2xl font-medium text-white">{adCampaigns.reduce((sum, c) => sum + c.leads, 0)}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <span className="text-2xl">👥</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Avg. Cost/Lead</p>
                  <p className="text-2xl font-medium text-white">
                    $
                    {Math.round(
                      adCampaigns.reduce((sum, c) => sum + c.spent, 0) /
                        adCampaigns.reduce((sum, c) => sum + c.leads, 0),
                    )}
                  </p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <span className="text-2xl">📊</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Campaigns List */}
        <div className="space-y-6">
          {adCampaigns.map((campaign) => (
            <Card key={campaign.id} className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-base font-medium text-white">
                      <Megaphone className="h-4 w-4" />
                      {campaign.name}
                    </CardTitle>
                    <p className="text-neutral-400 mt-1 text-sm">
                      {campaign.platform} • {campaign.startDate} - {campaign.endDate}
                    </p>
                  </div>
                  {getStatusBadge(campaign.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-neutral-400">Budget</span>
                      <span className="font-medium text-white">
                        ${campaign.spent} / ${campaign.budget}
                      </span>
                    </div>
                    <Progress value={(campaign.spent / campaign.budget) * 100} className="h-2 bg-neutral-800" />
                    <p className="text-xs text-neutral-500">
                      {Math.round((campaign.spent / campaign.budget) * 100)}% spent
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="text-xl font-medium text-white">{campaign.impressions.toLocaleString()}</div>
                    <p className="text-sm text-neutral-400">Impressions</p>
                  </div>

                  <div className="text-center">
                    <div className="text-xl font-medium text-white">{campaign.clicks}</div>
                    <p className="text-sm text-neutral-400">Clicks</p>
                  </div>

                  <div className="text-center">
                    <div className="text-xl font-medium text-white">{campaign.leads}</div>
                    <p className="text-sm text-neutral-400">Leads</p>
                  </div>

                  <div className="text-center">
                    <div className="text-xl font-medium text-white">
                      {((campaign.clicks / campaign.impressions) * 100).toFixed(1)}%
                    </div>
                    <p className="text-sm text-neutral-400">CTR</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  >
                    {campaign.status === "active" ? (
                      <>
                        <Pause className="h-4 w-4 mr-1" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-1" />
                        Resume
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    Analytics
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Ad
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {adCampaigns.length === 0 && (
          <Card className="bg-neutral-900 border-neutral-800">
            <CardContent className="text-center py-12">
              <Megaphone className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No campaigns yet</h3>
              <p className="text-neutral-400 mb-4">Create your first advertising campaign to get started</p>
              <Button className="bg-blue-gradient-hover text-white shadow-lg">
                <Plus className="h-4 w-4 mr-2" />
                Create Campaign
              </Button>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
