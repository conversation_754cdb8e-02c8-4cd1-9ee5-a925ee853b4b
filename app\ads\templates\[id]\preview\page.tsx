"use client"

import { useState, use } from "react"
import { use<PERSON><PERSON>er, useSearchPara<PERSON> } from "next/navigation"
import { notFound } from "next/navigation"
import { ArrowLeft, Download, Heart, Share2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Navigation } from "@/components/navigation"
import { CampaignPreview } from "@/components/campaign-preview"

// Mock data - in a real app, this would come from an API based on the ID
const getTemplateById = (id: string) => {
  const templates = [
    {
      id: 1,
      title: "Spring Clean-Up Package Special",
      category: "Seasonal",
      likes: 342,
      downloads: 156,
      image: "/placeholder.svg?height=350&width=300",
      tags: ["spring", "house-wash", "driveway", "bundle"],
      description: "House Wash + Driveway Cleaning + Gutter Clean-Out",
    },
    {
      id: 2,
      title: "Before & After: Roof Soft Washing",
      category: "Before/After",
      likes: 289,
      downloads: 134,
      image: "/placeholder.svg?height=400&width=300",
      tags: ["roof", "algae-removal", "soft-wash", "transformation"],
      description: "Safe removal of algae, moss & lichen from shingles",
    },
    {
      id: 3,
      title: "Commercial Fleet Washing Service",
      category: "Commercial",
      likes: 198,
      downloads: 89,
      image: "/placeholder.svg?height=320&width=300",
      tags: ["fleet", "commercial", "trucks", "on-location"],
      description: "Professional on-location fleet washing services",
    },
    {
      id: 4,
      title: "Neighborhood Group Discount",
      category: "Promotional",
      likes: 456,
      downloads: 234,
      image: "/placeholder.svg?height=360&width=300",
      tags: ["group-discount", "neighbors", "community", "savings"],
      description: "Book with 3+ neighbors and everyone saves 20%!",
    },
    {
      id: 5,
      title: "House Washing: All Siding Types",
      category: "Residential",
      likes: 378,
      downloads: 167,
      image: "/placeholder.svg?height=340&width=300",
      tags: ["house-wash", "vinyl", "stucco", "brick", "soft-wash"],
      description: "Safe soft washing for vinyl, stucco, wood & brick",
    },
    {
      id: 6,
      title: "Dumpster Pad Sanitization",
      category: "Commercial",
      likes: 145,
      downloads: 67,
      image: "/placeholder.svg?height=300&width=300",
      tags: ["sanitization", "hot-water", "commercial", "odor-removal"],
      description: "Hot water cleaning to eliminate odors & pests",
    },
  ]

  return templates.find(t => t.id === parseInt(id))
}

interface TemplatePreviewPageProps {
  params: Promise<{
    id: string
  }>
}

export default function TemplatePreviewPage({ params }: TemplatePreviewPageProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const resolvedParams = use(params)
  const template = getTemplateById(resolvedParams.id)

  // If template not found, show 404 page
  if (!template) {
    notFound()
  }

  // Determine which section we came from based on the referrer or search params
  const from = searchParams.get('from') || 'my-templates'
  const activeTab = from === 'library' ? 'ads-library' : 'my-templates'
  
  const [customData, setCustomData] = useState({
    headline: "Professional Pressure Washing Services",
    description:
      "Transform your property with our expert pressure washing services. Free estimates, fully insured, satisfaction guaranteed!",
    cta: "Get_Free_Quote",
    websiteUrl: "www.pressuremax.com",
    campaignName: template.title,
  })

  const mockTemplate = {
    id: template.id,
    title: template.title,
    category: template.category,
    creative: {
      media_requirements: {
        image_specs: ["1200x628", "1080x1080", "1080x1920"],
      },
    },
  }

  const updateCustomData = (field: keyof typeof customData, value: string) => {
    setCustomData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab={activeTab} />

      <main className="ml-64">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="text-neutral-400 hover:text-white hover:bg-neutral-800"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-medium text-white">{template.title}</h1>
              <p className="text-neutral-400 mt-1">Customize and preview your campaign</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Heart className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-[calc(100vh-200px)]">
          {/* Left Panel - Customization */}
          <div className="w-1/3 flex flex-col border-r border-neutral-800">
            <div className="p-6 pb-4">
              <h3 className="text-lg font-medium text-white mb-4">Customize Your Ad</h3>
            </div>
            <div className="flex-1 px-6 overflow-y-auto">{/* Scrollable content area */}

              <div className="space-y-4">
                <div>
                  <Label htmlFor="headline" className="text-white">
                    Headline
                  </Label>
                  <Input
                    id="headline"
                    value={customData.headline}
                    onChange={(e) => updateCustomData("headline", e.target.value)}
                    placeholder="Enter your headline"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>

                <div>
                  <Label htmlFor="description" className="text-white">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={customData.description}
                    onChange={(e) => updateCustomData("description", e.target.value)}
                    placeholder="Enter your ad description"
                    rows={4}
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>

                <div>
                  <Label htmlFor="cta" className="text-white">
                    Call to Action
                  </Label>
                  <Select value={customData.cta} onValueChange={(value) => updateCustomData("cta", value)}>
                    <SelectTrigger className="bg-neutral-800 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-neutral-900 border-neutral-800">
                      <SelectItem
                        value="Get_Free_Quote"
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        Get Free Quote
                      </SelectItem>
                      <SelectItem value="Call_Now" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Call Now
                      </SelectItem>
                      <SelectItem value="Book_Service" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Book Service
                      </SelectItem>
                      <SelectItem value="Learn_More" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Learn More
                      </SelectItem>
                      <SelectItem
                        value="Schedule_Today"
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        Schedule Today
                      </SelectItem>
                      <SelectItem value="Contact_Us" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Contact Us
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="website" className="text-white">
                    Website URL
                  </Label>
                  <Input
                    id="website"
                    value={customData.websiteUrl}
                    onChange={(e) => updateCustomData("websiteUrl", e.target.value)}
                    placeholder="www.yourwebsite.com"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>

                <div>
                  <Label htmlFor="campaignName" className="text-white">
                    Campaign Name
                  </Label>
                  <Input
                    id="campaignName"
                    value={customData.campaignName}
                    onChange={(e) => updateCustomData("campaignName", e.target.value)}
                    placeholder="Enter campaign name"
                    className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>

                <div className="pt-6 border-t border-neutral-800">
                  <h4 className="font-medium text-white mb-3">Template Info</h4>
                  <div className="space-y-2 text-sm text-neutral-400">
                    <p>
                      <strong className="text-white">Category:</strong> {template.category}
                    </p>
                    <p>
                      <strong className="text-white">Tags:</strong> {template.tags.join(", ")}
                    </p>
                    <p>
                      <strong className="text-white">Description:</strong> {template.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="flex-1 bg-neutral-800 flex flex-col">
            <div className="flex-1 p-6 overflow-hidden">
              <CampaignPreview template={mockTemplate} customData={customData} />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-neutral-800 bg-black">
          <div className="text-sm text-neutral-400">Preview updates in real-time as you make changes</div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button className="bg-blue-gradient-hover text-white shadow-lg">Use This Template</Button>
          </div>
        </div>
      </main>
    </div>
  )
}
