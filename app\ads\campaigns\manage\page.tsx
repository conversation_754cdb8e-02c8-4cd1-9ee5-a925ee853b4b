"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Copy,
  Play,
  Pause,
  Trash2,
  Download,
  Filter,
  ChevronDown,
  MoreHorizontal,
  TrendingUp,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Card, CardContent } from "@/components/ui/card"
import { Navigation } from "@/components/navigation"

const campaigns = [
  {
    id: 1,
    name: "Spring Clean-Up Special | Residential | Local",
    status: "Active",
    delivery: "Active",
    results: 47,
    costPerResult: "$24.78",
    budget: "Using ad set bud...",
    amountSpent: "$2,168.25",
    resultType: "Leads",
    objective: "Lead Generation",
    image: "/placeholder.svg?height=60&width=80",
  },
  {
    id: 2,
    name: "Commercial Fleet Washing | B2B | Lookalike",
    status: "Active",
    delivery: "Active",
    results: 8,
    costPerResult: "$41.56",
    budget: "Using ad set bud...",
    amountSpent: "$332.48",
    resultType: "Leads",
    objective: "Lead Generation",
    image: "/placeholder.svg?height=60&width=80",
  },
  {
    id: 3,
    name: "Roof Soft Washing | Retargeting | FB Ads",
    status: "Inactive",
    delivery: "Inactive",
    results: 12,
    costPerResult: "$26.04",
    budget: "Using ad set bud...",
    amountSpent: "$312.48",
    resultType: "Leads",
    objective: "Lead Generation",
    image: "/placeholder.svg?height=60&width=80",
  },
]

export default function CampaignManager() {
  const [selectedCampaigns, setSelectedCampaigns] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("campaigns")

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(campaigns.map((c) => c.id))
    } else {
      setSelectedCampaigns([])
    }
  }

  const handleSelectCampaign = (campaignId: number, checked: boolean) => {
    if (checked) {
      setSelectedCampaigns([...selectedCampaigns, campaignId])
    } else {
      setSelectedCampaigns(selectedCampaigns.filter((id) => id !== campaignId))
    }
  }

  const totalResults = campaigns.reduce((sum, campaign) => sum + campaign.results, 0)
  const totalSpent = campaigns.reduce(
    (sum, campaign) => sum + Number.parseFloat(campaign.amountSpent.replace(/[$,]/g, "")),
    0,
  )

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="ads-campaigns" />

      <main className="ml-64">
        {/* Header */}
        <div className="bg-neutral-900 border-b border-neutral-800">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2 text-sm text-neutral-400 mb-2">
                  <span>Account: Miller & Sons Marketing</span>
                  <ChevronDown className="w-4 h-4" />
                </div>
                <h1 className="text-2xl font-medium text-white">Campaign Manager</h1>
              </div>
              <Button className="bg-blue-gradient-hover text-white shadow-lg">
                <Plus className="w-4 h-4 mr-2" />
                Create Campaign
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-neutral-900 border-b border-neutral-800">
          <div className="px-6">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveTab("campaigns")}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "campaigns"
                    ? "border-blue-500 text-blue-400"
                    : "border-transparent text-neutral-400 hover:text-blue-300 hover:border-blue-500/40"
                }`}
              >
                Campaigns
                <Badge className="ml-2 bg-neutral-800 text-neutral-300">{campaigns.length}</Badge>
              </button>
              <button
                onClick={() => setActiveTab("adsets")}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "adsets"
                    ? "border-blue-500 text-blue-400"
                    : "border-transparent text-neutral-400 hover:text-blue-300 hover:border-blue-500/40"
                }`}
              >
                Ad Sets for 1 Campaign
              </button>
              <button
                onClick={() => setActiveTab("ads")}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "ads"
                    ? "border-blue-500 text-blue-400"
                    : "border-transparent text-neutral-400 hover:text-blue-300 hover:border-blue-500/40"
                }`}
              >
                Ads for 1 Campaign
              </button>
            </div>
          </div>
        </div>

        {/* Action Bar */}
        <div className="bg-neutral-900 border-b border-neutral-800 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                <Copy className="w-4 h-4 mr-2" />
                Duplicate
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                <Play className="w-4 h-4 mr-2" />
                Turn On
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                <Pause className="w-4 h-4 mr-2" />
                Turn Off
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  >
                    More
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-neutral-900 border-neutral-800">
                  <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-neutral-400">Columns:</span>
                <Select defaultValue="performance">
                  <SelectTrigger className="w-32 bg-neutral-800 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-900 border-neutral-800">
                    <SelectItem value="performance" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                      Performance
                    </SelectItem>
                    <SelectItem value="engagement" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                      Engagement
                    </SelectItem>
                    <SelectItem value="conversions" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                      Conversions
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-neutral-900 border-b border-neutral-800 px-6 py-3">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search campaigns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <div className="text-sm text-neutral-400">This month: Nov 1, 2017 - Nov 13, 2017</div>
          </div>
        </div>

        {/* Campaign Table */}
        <div className="bg-neutral-900">
          <Card className="bg-neutral-900 border-neutral-800 rounded-none">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-neutral-800 border-b border-neutral-700">
                    <tr>
                      <th className="px-6 py-3 text-left">
                        <Checkbox
                          checked={selectedCampaigns.length === campaigns.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Image
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Campaign Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Delivery
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Results
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Cost per Result
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Amount Spent
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-neutral-800">
                    {campaigns.map((campaign) => (
                      <tr key={campaign.id} className="hover:bg-blue-500/5">
                        <td className="px-6 py-4">
                          <Checkbox
                            checked={selectedCampaigns.includes(campaign.id)}
                            onCheckedChange={(checked) => handleSelectCampaign(campaign.id, checked as boolean)}
                          />
                        </td>
                        <td className="px-6 py-4">
                          <img
                            src={campaign.image || "/placeholder.svg"}
                            alt={campaign.name}
                            className="w-20 h-15 object-cover rounded-md border border-neutral-700"
                          />
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-3 h-3 rounded-full ${
                                campaign.status === "Active" ? "bg-blue-500" : "bg-neutral-600"
                              }`}
                            />
                            <div>
                              <div className="text-sm font-medium text-white">{campaign.name}</div>
                              <div className="text-xs text-neutral-400">{campaign.objective}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Badge
                            className={
                              campaign.delivery === "Active"
                                ? "bg-blue-500/10 text-blue-400 border-blue-500/20"
                                : "bg-neutral-500/10 text-neutral-400 border-neutral-500/20"
                            }
                          >
                            {campaign.delivery}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 text-sm text-white">
                          <div>{campaign.results}</div>
                          <div className="text-xs text-neutral-400">{campaign.resultType}</div>
                        </td>
                        <td className="px-6 py-4 text-sm text-white">{campaign.costPerResult}</td>
                        <td className="px-6 py-4 text-sm text-white font-medium">{campaign.amountSpent}</td>
                        <td className="px-6 py-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                              >
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-neutral-900 border-neutral-800">
                              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                                <Edit className="w-4 h-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                                <Copy className="w-4 h-4 mr-2" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                                <TrendingUp className="w-4 h-4 mr-2" />
                                View Charts
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}

                    {/* Summary Row */}
                    <tr className="bg-neutral-800 font-medium border-t-2 border-neutral-600">
                      <td className="px-6 py-4"></td>
                      <td className="px-6 py-4"></td>
                      <td className="px-6 py-4 text-sm text-white">Results from {campaigns.length} campaigns</td>
                      <td className="px-6 py-4"></td>
                      <td className="px-6 py-4 text-sm text-white">{totalResults}</td>
                      <td className="px-6 py-4 text-sm text-white">${(totalSpent / totalResults).toFixed(2)}</td>
                      <td className="px-6 py-4 text-sm text-white font-bold">
                        ${totalSpent.toLocaleString("en-US", { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
