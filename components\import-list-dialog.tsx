"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Upload, FileText, AlertCircle } from "lucide-react"

interface ImportListDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImportListDialog({ open, onOpenChange }: ImportListDialogProps) {
  const [importType, setImportType] = useState("")
  const [listName, setListName] = useState("")
  const [file, setFile] = useState<File | null>(null)

  const handleImport = () => {
    console.log("Importing list:", { importType, listName, file })
    onOpenChange(false)
    setImportType("")
    setListName("")
    setFile(null)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      if (!listName) {
        setListName(selectedFile.name.replace(/\.[^/.]+$/, ""))
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-white">Import New Lead List</DialogTitle>
          <DialogDescription className="text-neutral-400">Add a new list of leads to your database</DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div>
            <Label htmlFor="list-name" className="text-white">
              List Name
            </Label>
            <Input
              id="list-name"
              placeholder="e.g., Q3 Website Leads"
              value={listName}
              onChange={(e) => setListName(e.target.value)}
              className="bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
            />
          </div>

          <div>
            <Label className="text-base font-medium text-white">Import Method</Label>
            <RadioGroup value={importType} onValueChange={setImportType} className="mt-3">
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
                  <RadioGroupItem value="csv" id="csv" />
                  <div className="flex items-center space-x-3 flex-1">
                    <FileText className="h-5 w-5 text-blue-400" />
                    <div>
                      <Label htmlFor="csv" className="font-medium text-white">
                        Upload CSV File
                      </Label>
                      <p className="text-sm text-neutral-400">Import from a CSV file with contact information</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
                  <RadioGroupItem value="manual" id="manual" />
                  <div className="flex items-center space-x-3 flex-1">
                    <Upload className="h-5 w-5 text-blue-400" />
                    <div>
                      <Label htmlFor="manual" className="font-medium text-white">
                        Manual Entry
                      </Label>
                      <p className="text-sm text-neutral-400">Add contacts one by one manually</p>
                    </div>
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>

          {importType === "csv" && (
            <div>
              <Label className="text-white">CSV File</Label>
              <div className="mt-2 p-4 border-2 border-dashed border-neutral-700 rounded-lg text-center">
                {file ? (
                  <div className="flex items-center justify-center gap-2">
                    <FileText className="h-6 w-6 text-blue-400" />
                    <span className="text-sm text-white">{file.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFile(null)}
                      className="text-neutral-400 hover:text-white"
                    >
                      Remove
                    </Button>
                  </div>
                ) : (
                  <>
                    <Upload className="h-8 w-8 text-neutral-500 mx-auto mb-2" />
                    <p className="text-sm text-neutral-400 mb-2">Drop your CSV file here or click to browse</p>
                    <input type="file" accept=".csv" onChange={handleFileChange} className="hidden" id="file-upload" />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => document.getElementById("file-upload")?.click()}
                      className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    >
                      Choose File
                    </Button>
                  </>
                )}
              </div>

              <div className="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-blue-400 mt-0.5" />
                  <div className="text-sm">
                    <p className="text-blue-400 font-medium">CSV Format Requirements</p>
                    <p className="text-blue-300 mt-1">
                      Include columns: Name, Phone, Email (optional). First row should contain headers.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
          >
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            disabled={!listName || !importType || (importType === "csv" && !file)}
            className="bg-blue-gradient-hover text-white shadow-lg"
          >
            {importType === "csv" ? "Import List" : "Create List"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
