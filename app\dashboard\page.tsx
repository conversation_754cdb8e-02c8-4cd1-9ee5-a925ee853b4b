"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  DollarSign, 
  TrendingUp, 
  Phone, 
  Calendar,
  Droplets,
  Bell,
  Settings,
  LogOut,
  Play,
  Pause,
  Eye
} from "lucide-react"
import { PressureMaxNavigation } from "@/components/pressuremax-navigation"

export default function DashboardPage() {
  const [dateRange, setDateRange] = useState("This Month")

  // Mock data - in real app this would come from API
  const dashboardData = {
    newLeadsToday: 8,
    activeCampaigns: 3,
    totalAdSpend: 1247,
    costPerLead: 28,
    recentLeads: [
      {
        id: 1,
        name: "<PERSON>",
        phone: "(*************",
        email: "<EMAIL>",
        source: "Driveway Revival Campaign",
        time: "2 hours ago",
        status: "new"
      },
      {
        id: 2,
        name: "<PERSON>",
        phone: "(*************",
        email: "<EMAIL>",
        source: "Spring House Wash",
        time: "4 hours ago",
        status: "contacted"
      },
      {
        id: 3,
        name: "<PERSON>",
        phone: "(*************",
        email: "<EMAIL>",
        source: "Deck Cleaning Special",
        time: "6 hours ago",
        status: "quote_sent"
      },
      {
        id: 4,
        name: "Tom <PERSON>",
        phone: "(*************",
        email: "<EMAIL>",
        source: "Gutter Clean-Out",
        time: "1 day ago",
        status: "won"
      },
      {
        id: 5,
        name: "Amy <PERSON>",
        phone: "(*************",
        email: "<EMAIL>",
        source: "Commercial Fleet Wash",
        time: "1 day ago",
        status: "new"
      }
    ],
    activeCampaignsList: [
      {
        id: 1,
        name: "Spring Driveway Revival",
        status: "active",
        dailyBudget: 35,
        leadsToday: 3,
        costPerLead: 24
      },
      {
        id: 2,
        name: "House Wash Special",
        status: "active",
        dailyBudget: 50,
        leadsToday: 4,
        costPerLead: 31
      },
      {
        id: 3,
        name: "Deck Cleaning Promo",
        status: "paused",
        dailyBudget: 25,
        leadsToday: 1,
        costPerLead: 22
      }
    ]
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      new: { label: "New Lead", className: "bg-blue-100 text-blue-800" },
      contacted: { label: "Contacted", className: "bg-yellow-100 text-yellow-800" },
      quote_sent: { label: "Quote Sent", className: "bg-purple-100 text-purple-800" },
      won: { label: "Job Won", className: "bg-green-100 text-green-800" },
      lost: { label: "Job Lost", className: "bg-red-100 text-red-800" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.new
    return <Badge className={config.className}>{config.label}</Badge>
  }

  const getCampaignStatusBadge = (status: string) => {
    return status === "active" ? (
      <Badge className="bg-green-100 text-green-800">Active</Badge>
    ) : (
      <Badge className="bg-gray-100 text-gray-800">Paused</Badge>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <PressureMaxNavigation activeTab="dashboard" />
      
      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your campaigns.</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" className="border-blue-200 text-blue-600 hover:bg-blue-50">
              <Calendar className="h-4 w-4 mr-2" />
              {dateRange}
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Bell className="h-4 w-4 mr-2" />
              View All Notifications
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">New Leads Today</p>
                  <p className="text-3xl font-bold text-gray-900">{dashboardData.newLeadsToday}</p>
                  <p className="text-sm text-green-600 mt-1">+2 from yesterday</p>
                </div>
                <div className="bg-blue-100 rounded-full p-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Campaigns</p>
                  <p className="text-3xl font-bold text-gray-900">{dashboardData.activeCampaigns}</p>
                  <p className="text-sm text-gray-500 mt-1">2 performing well</p>
                </div>
                <div className="bg-green-100 rounded-full p-3">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Ad Spend (MTD)</p>
                  <p className="text-3xl font-bold text-gray-900">${dashboardData.totalAdSpend}</p>
                  <p className="text-sm text-gray-500 mt-1">$42/day average</p>
                </div>
                <div className="bg-purple-100 rounded-full p-3">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cost Per Lead (MTD)</p>
                  <p className="text-3xl font-bold text-gray-900">${dashboardData.costPerLead}</p>
                  <p className="text-sm text-green-600 mt-1">-$3 from last month</p>
                </div>
                <div className="bg-orange-100 rounded-full p-3">
                  <Phone className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Leads */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-semibold">Recent Leads</CardTitle>
              <Button variant="outline" size="sm">
                View All Leads
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.recentLeads.map((lead) => (
                  <div key={lead.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium text-gray-900">{lead.name}</h4>
                        {getStatusBadge(lead.status)}
                      </div>
                      <p className="text-sm text-gray-600">{lead.phone} • {lead.email}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        From: {lead.source} • {lead.time}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Active Campaigns */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-semibold">Active Campaigns</CardTitle>
              <Button variant="outline" size="sm">
                Manage Campaigns
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.activeCampaignsList.map((campaign) => (
                  <div key={campaign.id} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{campaign.name}</h4>
                      {getCampaignStatusBadge(campaign.status)}
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Daily Budget</p>
                        <p className="font-medium">${campaign.dailyBudget}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Leads Today</p>
                        <p className="font-medium">{campaign.leadsToday}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Cost/Lead</p>
                        <p className="font-medium">${campaign.costPerLead}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-3">
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          {campaign.status === "active" ? (
                            <>
                              <Pause className="h-3 w-3 mr-1" />
                              Pause
                            </>
                          ) : (
                            <>
                              <Play className="h-3 w-3 mr-1" />
                              Resume
                            </>
                          )}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
