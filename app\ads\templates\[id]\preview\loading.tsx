import { Navigation } from "@/components/navigation"
import { Skeleton } from "@/components/ui/skeleton"

export default function TemplatePreviewLoading() {
  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="ads-library" />

      <main className="ml-64">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-16 bg-neutral-800" />
            <div>
              <Skeleton className="h-8 w-64 bg-neutral-800 mb-2" />
              <Skeleton className="h-4 w-48 bg-neutral-800" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-20 bg-neutral-800" />
            <Skeleton className="h-8 w-20 bg-neutral-800" />
          </div>
        </div>

        {/* Content */}
        <div className="flex h-[calc(100vh-200px)]">
          {/* Left Panel - Customization */}
          <div className="w-1/3 flex flex-col border-r border-neutral-800">
            <div className="p-6 pb-4">
              <Skeleton className="h-6 w-32 bg-neutral-800 mb-4" />
            </div>
            <div className="flex-1 px-6 overflow-y-auto">
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i}>
                    <Skeleton className="h-4 w-20 bg-neutral-800 mb-2" />
                    <Skeleton className="h-10 w-full bg-neutral-800" />
                  </div>
                ))}

                <div className="pt-6 border-t border-neutral-800">
                  <Skeleton className="h-5 w-24 bg-neutral-800 mb-3" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full bg-neutral-800" />
                    <Skeleton className="h-4 w-3/4 bg-neutral-800" />
                    <Skeleton className="h-4 w-5/6 bg-neutral-800" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="flex-1 bg-neutral-800 flex flex-col">
            <div className="flex-1 p-6 overflow-hidden">
              <div className="space-y-4">
                <Skeleton className="h-8 w-48 bg-neutral-700" />
                <Skeleton className="h-64 w-full bg-neutral-700" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full bg-neutral-700" />
                  <Skeleton className="h-4 w-3/4 bg-neutral-700" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-neutral-800 bg-black">
          <Skeleton className="h-4 w-64 bg-neutral-800" />
          <div className="flex items-center space-x-3">
            <Skeleton className="h-8 w-20 bg-neutral-800" />
            <Skeleton className="h-8 w-32 bg-neutral-800" />
          </div>
        </div>
      </main>
    </div>
  )
}
