{"Commands:": "Polecenia:", "Options:": "Opcje:", "Examples:": "Przykłady:", "boolean": "boolean", "count": "<PERSON><PERSON><PERSON><PERSON>", "string": "ciąg znaków", "number": "liczba", "array": "tablica", "required": "wym<PERSON>y", "default": "domyślny", "default:": "domyślny:", "choices:": "dostępne:", "aliases:": "aliasy:", "generated-value": "<PERSON><PERSON><PERSON>row<PERSON>-<PERSON><PERSON><PERSON>", "Not enough non-option arguments: got %s, need at least %s": {"one": "Niewystarczająca ilość argumentów: otrzymano %s, wymagane co najmniej %s", "other": "Niewystarczająca ilość argumentów: otrzymano %s, wymagane co najmniej %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "Zbyt duża ilość argumentów: otrzymano %s, wymagane co najwyżej %s", "other": "Zbyt duża ilość argumentów: otrzymano %s, wymagane co najwyżej %s"}, "Missing argument value: %s": {"one": "<PERSON>rak wartości dla argumentu: %s", "other": "Brak wartości dla argumentów: %s"}, "Missing required argument: %s": {"one": "Brak wymaganego argumentu: %s", "other": "Brak wymaganych argumentów: %s"}, "Unknown argument: %s": {"one": "Nieznany argument: %s", "other": "<PERSON>eznane argumenty: %s"}, "Invalid values:": "Nieprawidłowe wartości:", "Argument: %s, Given: %s, Choices: %s": "Argument: %s, Otrzymano: %s, Dostępne: %s", "Argument check failed: %s": "Weryfikacja argumentów nie powiodła się: %s", "Implications failed:": "Założenia nie zostały spełnione:", "Not enough arguments following: %s": "Niewystarczająca ilość argumentów następujących po: %s", "Invalid JSON config file: %s": "Nieprawidłowy plik konfiguracyjny JSON: %s", "Path to JSON config file": "Ścieżka do pliku konfiguracyjnego JSON", "Show help": "Pokaż pomoc", "Show version number": "Pokaż numer wersji", "Did you mean %s?": "<PERSON><PERSON> chodziło Ci o %s?", "Arguments %s and %s are mutually exclusive": "Argumenty %s i %s wzajemnie się wykluczają", "Positionals:": "Pozycyjne:", "command": "polecenie"}