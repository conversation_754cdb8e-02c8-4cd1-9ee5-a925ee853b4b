{"name": "commander", "version": "9.5.0", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "test": "jest && npm run test-typings", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm"}, "files": ["index.js", "lib/*.js", "esm.mjs", "typings/index.d.ts", "package-support.json"], "type": "commonjs", "main": "./index.js", "exports": {".": {"types": "./typings/index.d.ts", "require": "./index.js", "import": "./esm.mjs"}, "./esm.mjs": "./esm.mjs"}, "devDependencies": {"@types/jest": "^28.1.4", "@types/node": "^16.11.15", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "eslint": "^8.19.0", "eslint-config-standard": "^17.0.0", "eslint-config-standard-with-typescript": "^22.0.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-n": "^15.2.4", "eslint-plugin-promise": "^6.0.0", "jest": "^28.1.2", "ts-jest": "^28.0.5", "tsd": "^0.22.0", "typescript": "^4.7.4"}, "types": "typings/index.d.ts", "jest": {"testEnvironment": "node", "collectCoverage": true, "transform": {"^.+\\.tsx?$": "ts-jest"}, "testPathIgnorePatterns": ["/node_modules/"]}, "engines": {"node": "^12.20.0 || >=14"}, "support": true}