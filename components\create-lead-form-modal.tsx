"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Trash2, GripVertical, Eye } from "lucide-react"

interface CreateLeadFormModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface FormField {
  id: string
  type: string
  label: string
  placeholder: string
  required: boolean
  options?: string[]
}

const FIELD_TYPES = [
  { value: "text", label: "Text Input" },
  { value: "email", label: "Email" },
  { value: "phone", label: "Phone Number" },
  { value: "select", label: "Dropdown" },
  { value: "radio", label: "Multiple Choice" },
  { value: "checkbox", label: "Checkboxes" },
  { value: "textarea", label: "Text Area" },
]

export function CreateLeadFormModal({ open, onOpenChange }: CreateLeadFormModalProps) {
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    thankYouMessage: "Thank you for your interest! We'll contact you within 24 hours.",
    privacyPolicy: "We respect your privacy and will never share your information.",
  })

  const [fields, setFields] = useState<FormField[]>([
    {
      id: "1",
      type: "text",
      label: "Full Name",
      placeholder: "Enter your full name",
      required: true,
    },
    {
      id: "2",
      type: "email",
      label: "Email Address",
      placeholder: "Enter your email",
      required: true,
    },
    {
      id: "3",
      type: "phone",
      label: "Phone Number",
      placeholder: "Enter your phone number",
      required: true,
    },
  ])

  const addField = () => {
    const newField: FormField = {
      id: Date.now().toString(),
      type: "text",
      label: "New Field",
      placeholder: "Enter placeholder text",
      required: false,
    }
    setFields([...fields, newField])
  }

  const removeField = (id: string) => {
    setFields(fields.filter((field) => field.id !== id))
  }

  const updateField = (id: string, updates: Partial<FormField>) => {
    setFields(fields.map((field) => (field.id === id ? { ...field, ...updates } : field)))
  }

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleCreate = () => {
    console.log("Creating form:", { formData, fields })
    onOpenChange(false)
    setStep(1)
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="form-name" className="text-white">
          Form Name *
        </Label>
        <Input
          id="form-name"
          value={formData.name}
          onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
          placeholder="e.g., Free Pressure Washing Quote"
          className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
        />
      </div>

      <div>
        <Label htmlFor="description" className="text-white">
          Description
        </Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
          placeholder="Brief description of what this form is for"
          rows={3}
          className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
        />
      </div>

      <div>
        <Label htmlFor="thank-you" className="text-white">
          Thank You Message
        </Label>
        <Textarea
          id="thank-you"
          value={formData.thankYouMessage}
          onChange={(e) => setFormData((prev) => ({ ...prev, thankYouMessage: e.target.value }))}
          rows={3}
          className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
        />
      </div>

      <div>
        <Label htmlFor="privacy" className="text-white">
          Privacy Policy Text
        </Label>
        <Textarea
          id="privacy"
          value={formData.privacyPolicy}
          onChange={(e) => setFormData((prev) => ({ ...prev, privacyPolicy: e.target.value }))}
          rows={2}
          className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
        />
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white">Form Fields</h3>
        <Button onClick={addField} size="sm" className="bg-blue-gradient-hover text-white shadow-lg">
          <Plus className="h-4 w-4 mr-1" />
          Add Field
        </Button>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {fields.map((field, index) => (
          <Card key={field.id} className="bg-neutral-800 border-neutral-700">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GripVertical className="h-4 w-4 text-neutral-500" />
                  <span className="text-sm font-medium text-white">Field {index + 1}</span>
                </div>
                <Button
                  onClick={() => removeField(field.id)}
                  variant="ghost"
                  size="sm"
                  className="text-red-400 hover:text-red-300 hover:bg-blue-500/10"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-white text-xs">Field Type</Label>
                  <Select value={field.type} onValueChange={(value) => updateField(field.id, { type: value })}>
                    <SelectTrigger className="bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-neutral-900 border-neutral-800">
                      {FIELD_TYPES.map((type) => (
                        <SelectItem
                          key={type.value}
                          value={type.value}
                          className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                        >
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2 pt-6">
                  <Checkbox
                    id={`required-${field.id}`}
                    checked={field.required}
                    onCheckedChange={(checked) => updateField(field.id, { required: checked as boolean })}
                  />
                  <Label htmlFor={`required-${field.id}`} className="text-white text-xs">
                    Required
                  </Label>
                </div>
              </div>

              <div>
                <Label className="text-white text-xs">Label</Label>
                <Input
                  value={field.label}
                  onChange={(e) => updateField(field.id, { label: e.target.value })}
                  className="bg-neutral-900 border-neutral-600 text-white"
                />
              </div>

              <div>
                <Label className="text-white text-xs">Placeholder</Label>
                <Input
                  value={field.placeholder}
                  onChange={(e) => updateField(field.id, { placeholder: e.target.value })}
                  className="bg-neutral-900 border-neutral-600 text-white"
                />
              </div>

              {(field.type === "select" || field.type === "radio" || field.type === "checkbox") && (
                <div>
                  <Label className="text-white text-xs">Options (one per line)</Label>
                  <Textarea
                    value={field.options?.join("\n") || ""}
                    onChange={(e) => updateField(field.id, { options: e.target.value.split("\n").filter(Boolean) })}
                    placeholder="Option 1&#10;Option 2&#10;Option 3"
                    rows={3}
                    className="bg-neutral-900 border-neutral-600 text-white"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white">Preview</h3>
        <div className="flex items-center gap-2 text-sm text-neutral-400">
          <Eye className="h-4 w-4" />
          <span>Form Preview</span>
        </div>
      </div>

      <Card className="bg-white text-black max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-lg">{formData.name || "Form Name"}</CardTitle>
          {formData.description && <p className="text-sm text-gray-600">{formData.description}</p>}
        </CardHeader>
        <CardContent className="space-y-4">
          {fields.map((field) => (
            <div key={field.id}>
              <Label className="text-sm font-medium text-gray-900">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {field.type === "textarea" ? (
                <Textarea placeholder={field.placeholder} className="mt-1" disabled />
              ) : field.type === "select" ? (
                <Select disabled>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder={field.placeholder} />
                  </SelectTrigger>
                </Select>
              ) : field.type === "radio" ? (
                <div className="mt-2 space-y-2">
                  {field.options?.map((option, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <input type="radio" disabled className="text-blue-600" />
                      <span className="text-sm">{option}</span>
                    </div>
                  ))}
                </div>
              ) : field.type === "checkbox" ? (
                <div className="mt-2 space-y-2">
                  {field.options?.map((option, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <input type="checkbox" disabled className="text-blue-600" />
                      <span className="text-sm">{option}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <Input type={field.type} placeholder={field.placeholder} className="mt-1" disabled />
              )}
            </div>
          ))}

          <Button className="w-full bg-blue-gradient-hover" disabled>
            Submit
          </Button>

          <p className="text-xs text-gray-500 text-center">{formData.privacyPolicy}</p>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-white">Create Lead Form (Step {step} of 3)</DialogTitle>
          <DialogDescription className="text-neutral-400">
            {step === 1 && "Set up basic information for your lead form"}
            {step === 2 && "Configure the fields that will appear in your form"}
            {step === 3 && "Review your form before creating it"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </div>

        <DialogFooter className="flex justify-between">
          <div>
            {step > 1 && (
              <Button
                variant="outline"
                onClick={handleBack}
                className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
              >
                Back
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              Cancel
            </Button>
            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={step === 1 && !formData.name}
                className="bg-blue-gradient-hover text-white shadow-lg"
              >
                Next: {step === 1 ? "Fields" : "Preview"}
              </Button>
            ) : (
              <Button onClick={handleCreate} className="bg-blue-gradient-hover text-white shadow-lg">
                Create Form
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
