"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Search, Eye, Trash2, Download, Users, Phone } from "lucide-react"
import { Navigation } from "@/components/navigation"
import { ImportListDialog } from "@/components/import-list-dialog"
import { LeadDetailModal } from "@/components/lead-detail-modal"

const leadLists = [
  {
    id: 1,
    name: "Old Roof Leads",
    count: 5000,
    dateAdded: "June 12, 2025",
    status: "used",
    campaignCount: 1,
  },
  {
    id: 2,
    name: "Unclosed Deals 2024",
    count: 850,
    dateAdded: "May 28, 2025",
    status: "used",
    campaignCount: 2,
  },
  {
    id: 3,
    name: "Summer_Leads_2025.csv",
    count: 1245,
    dateAdded: "July 18, 2025",
    status: "ready",
    campaignCount: 0,
  },
  {
    id: 4,
    name: "New Website Leads",
    count: 43,
    dateAdded: "Dynamic List",
    status: "active",
    campaignCount: 0,
  },
]

const sampleContacts = [
  {
    id: 1,
    name: "John Smith",
    phone: "(*************",
    email: "<EMAIL>",
    lastCalled: "July 15, 2025",
    status: "booked",
    lists: ["Old Roof Leads", "Summer_Leads_2025"],
  },
  {
    id: 2,
    name: "Jane Doe",
    phone: "(*************",
    email: "<EMAIL>",
    lastCalled: "July 16, 2025",
    status: "no-answer",
    lists: ["Old Roof Leads"],
  },
  {
    id: 3,
    name: "Peter Jones",
    phone: "(*************",
    email: "<EMAIL>",
    lastCalled: "July 14, 2025",
    status: "disqualified",
    lists: ["Old Roof Leads"],
  },
  {
    id: 4,
    name: "Sarah Wilson",
    phone: "(*************",
    email: "<EMAIL>",
    lastCalled: "July 17, 2025",
    status: "interested",
    lists: ["Summer_Leads_2025"],
  },
  {
    id: 5,
    name: "Mike Johnson",
    phone: "(*************",
    email: "<EMAIL>",
    lastCalled: "July 13, 2025",
    status: "callback",
    lists: ["Unclosed Deals 2024"],
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "used":
      return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Used in Campaign</Badge>
    case "ready":
      return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Ready</Badge>
    case "active":
      return <Badge className="bg-purple-500/10 text-purple-400 border-purple-500/20">Active</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
  }
}

const getContactStatusBadge = (status: string) => {
  switch (status) {
    case "booked":
      return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Appt. Booked</Badge>
    case "interested":
      return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Interested</Badge>
    case "callback":
      return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Callback</Badge>
    case "no-answer":
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">No Answer</Badge>
    case "disqualified":
      return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Disqualified</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
  }
}

export default function LeadsPage() {
  const [activeTab, setActiveTab] = useState("lists")
  const [selectedList, setSelectedList] = useState<(typeof leadLists)[0] | null>(null)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [selectedLead, setSelectedLead] = useState<(typeof sampleContacts)[0] | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const filteredContacts = sampleContacts.filter((contact) => {
    const matchesSearch =
      contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.phone.includes(searchTerm) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || contact.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleViewList = (list: (typeof leadLists)[0]) => {
    setSelectedList(list)
    setActiveTab("contacts")
  }

  const handleBackToLists = () => {
    setSelectedList(null)
    setActiveTab("lists")
  }

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="leads" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Leads</h1>
            <p className="text-neutral-400 mt-1 text-sm">Manage your lead lists and contact database</p>
          </div>
          <Button
            onClick={() => setShowImportDialog(true)}
            className="flex items-center gap-2 bg-blue-gradient-hover text-white shadow-lg"
          >
            <Plus className="h-4 w-4" />
            Import New List
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-fit grid-cols-2 bg-neutral-900 border border-blue-500/20">
            <TabsTrigger
              value="lists"
              className="data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400 text-neutral-400 hover:bg-blue-500/5"
              onClick={handleBackToLists}
            >
              Lead Lists
            </TabsTrigger>
            <TabsTrigger
              value="contacts"
              className="data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400 text-neutral-400 hover:bg-blue-500/5"
            >
              {selectedList ? `${selectedList.name}` : "All Contacts"}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="lists" className="mt-6">
            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                  <Users className="h-4 w-4" />
                  Lead Lists
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-neutral-800">
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">List Name</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm"># of Leads</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Date Added</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {leadLists.map((list) => (
                        <tr key={list.id} className="border-b border-neutral-800 hover:bg-neutral-800/50">
                          <td className="py-3 px-4">
                            <div>
                              <p className="text-sm font-medium text-white">{list.name}</p>
                              {list.count >= 1000 && (
                                <p className="text-xs text-neutral-500">({list.count.toLocaleString()})</p>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4 text-sm text-white">{list.count.toLocaleString()}</td>
                          <td className="py-3 px-4 text-sm text-neutral-400">{list.dateAdded}</td>
                          <td className="py-3 px-4">
                            <div className="flex flex-col gap-1">
                              {getStatusBadge(list.status)}
                              {list.campaignCount > 0 && (
                                <span className="text-xs text-neutral-500">
                                  Used in {list.campaignCount} campaign{list.campaignCount > 1 ? "s" : ""}
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewList(list)}
                                className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contacts" className="mt-6">
            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                    <Phone className="h-4 w-4" />
                    {selectedList ? `${selectedList.name} (${selectedList.count.toLocaleString()})` : "All Contacts"}
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Lead
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Export List
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 h-4 w-4" />
                    <Input
                      placeholder="Search contacts in this list..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent className="bg-neutral-900 border-neutral-800">
                      <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Status: All
                      </SelectItem>
                      <SelectItem value="booked" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Appt. Booked
                      </SelectItem>
                      <SelectItem value="interested" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Interested
                      </SelectItem>
                      <SelectItem value="callback" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        Callback
                      </SelectItem>
                      <SelectItem value="no-answer" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        No Answer
                      </SelectItem>
                      <SelectItem
                        value="disqualified"
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        Disqualified
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Contacts Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-neutral-800">
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Contact Name</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Phone Number</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Last Called</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredContacts.map((contact) => (
                        <tr key={contact.id} className="border-b border-neutral-800 hover:bg-neutral-800/50">
                          <td className="py-3 px-4">
                            <div>
                              <p className="text-sm font-medium text-white">{contact.name}</p>
                              <p className="text-xs text-neutral-500">{contact.email}</p>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-sm text-neutral-400">{contact.phone}</td>
                          <td className="py-3 px-4 text-sm text-neutral-400">{contact.lastCalled}</td>
                          <td className="py-3 px-4">{getContactStatusBadge(contact.status)}</td>
                          <td className="py-3 px-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedLead(contact)}
                              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                            >
                              View Lead
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {filteredContacts.length === 0 && (
                  <div className="text-center py-12">
                    <Users className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No contacts found</h3>
                    <p className="text-neutral-400">
                      {searchTerm || statusFilter !== "all"
                        ? "Try adjusting your search or filters"
                        : "This list is empty. Import contacts to get started."}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <ImportListDialog open={showImportDialog} onOpenChange={setShowImportDialog} />
      <LeadDetailModal
        lead={selectedLead}
        open={!!selectedLead}
        onOpenChange={(open) => !open && setSelectedLead(null)}
      />
    </div>
  )
}
