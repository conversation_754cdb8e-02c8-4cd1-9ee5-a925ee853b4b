# AI VoiceConnect Frontend Analysis

## Overview
AI VoiceConnect is a comprehensive platform for managing AI voice agents and advertising campaigns. The frontend is built with Next.js 14, TypeScript, and Tailwind CSS, featuring a dark theme design system with blue accent colors.

## Main Navigation Structure

### Core Sections
1. **Dashboard** (`/`) - Main overview and analytics
2. **Ads** - Complete advertising management suite
3. **Voice Agents** - AI agent creation and management
4. **Analytics** - Detailed call logs and performance metrics
5. **Leads** - Lead list and contact management
6. **Settings** - Account and integration settings

---

## Pages and Features

### 1. Dashboard (`/`)
**Purpose**: Central hub for monitoring AI agents and campaign performance

**Key Features**:
- **KPI Cards**: 
  - Appointments Booked (142, +12%)
  - Calls Connected (1,109, +8%)
  - Positive Outcomes (78%, +5%)
- **Recent Activity Feed**: Real-time updates on agent actions
- **Active Agents & Campaigns Table**: Live status monitoring
- **Date Range Filtering**: Today, Last 7 Days, Last 30 Days, Custom Range

**Components Used**:
- `RecentActivity` - Activity timeline
- `ActiveAgentsTable` - Live agent status
- KPI cards with trend indicators

---

### 2. Ads Section

#### 2.1 Ad Library (`/ads`)
**Purpose**: Browse and import professional ad templates

**Key Features**:
- **Template Gallery**: 6+ categorized ad templates
- **Categories**: Seasonal, Before/After, Commercial, Promotional, Residential, etc.
- **Template Stats**: Likes, downloads, engagement metrics
- **Search & Filter**: By category, tags, keywords
- **View Modes**: Grid and list layouts
- **Template Actions**: Import, Preview, Customize
- **Stats Dashboard**: Total templates, downloads, categories, favorites

**Sample Templates**:
- Spring Clean-Up Package Special
- Before & After: Roof Soft Washing
- Commercial Fleet Washing Service
- Neighborhood Group Discount

#### 2.2 My Templates (`/ads/templates`)
**Purpose**: Manage custom and imported ad templates

**Key Features**:
- **Template Management**: Create, edit, duplicate, delete
- **Status Tracking**: Draft, Active, Paused, Archived
- **Campaign Integration**: Track template usage across campaigns
- **Custom Branding**: Personalized templates for business
- **Template Analytics**: Performance metrics per template
- **Bulk Actions**: Export, archive, organize templates

**Template Statuses**:
- Active: Currently in use
- Draft: Work in progress
- Paused: Temporarily disabled
- Archived: Stored for reference

#### 2.3 Lead Forms (`/ads/forms`)
**Purpose**: Create and manage lead capture forms for Facebook campaigns

**Key Features**:
- **Form Builder**: Custom field configuration
- **Performance Tracking**: Leads captured, conversion rates
- **Form Fields**: Name, Email, Phone, Property Type, Service Needed, etc.
- **Integration**: Direct connection to lead management
- **A/B Testing**: Multiple form variants
- **Analytics**: Conversion rate optimization

**Sample Forms**:
- Free Pressure Washing Quote (127 leads, 18.5% conversion)
- Commercial Fleet Washing Inquiry (23 leads, 31.2% conversion)
- Roof Cleaning Consultation (Draft)

#### 2.4 Ad Campaigns (`/ads/campaigns`)
**Purpose**: Manage advertising campaigns across platforms

**Key Features**:
- **Multi-Platform Support**: Facebook Ads, Google Ads, LinkedIn Ads
- **Budget Management**: Spend tracking, budget allocation
- **Performance Metrics**: Impressions, clicks, leads, CTR
- **Campaign Controls**: Pause, resume, edit campaigns
- **ROI Tracking**: Cost per lead, conversion metrics
- **Campaign Analytics**: Detailed performance reports

**Sample Campaigns**:
- Spring House Washing Promo (Facebook, $342/$500 spent)
- Roof Cleaning Before/After (Google, $156/$300 spent)
- Commercial Fleet Washing (LinkedIn, Paused)

---

### 3. Voice Agents Section

#### 3.1 Agents (`/agents`)
**Purpose**: Create and manage AI voice agents

**Key Features**:
- **Agent Types**:
  - Inbound Receptionist (handles incoming calls)
  - Outbound (New Leads) - Speed-to-Lead automation
  - Outbound (Campaign) - Bulk calling campaigns
- **Agent Management**: Create, edit, test, pause/activate
- **Real-time Testing**: Interactive voice testing with AI Orb Widget
- **Performance Analytics**: Individual agent metrics
- **Phone Number Assignment**: Dedicated lines per agent
- **Script Management**: Custom conversation flows

**Sample Agents**:
- Main Office Line (Inbound, (*************)
- Speed-to-Lead Agent (Outbound, triggers on new leads)
- Trash to Cash Agent (Campaign, uses Q2 Promo Script)

**Agent Testing**:
- Interactive AI Orb Widget with 3D visualization
- Real-time voice interaction
- Microphone input support
- Speaker state simulation (thinking, user, agent)

#### 3.2 Voice Campaigns (`/campaigns`)
**Purpose**: Manage outbound calling campaigns

**Key Features**:
- **Campaign Creation**: Target lead lists with specific agents
- **Progress Tracking**: Calls made vs. total leads
- **Appointment Booking**: Track successful conversions
- **Campaign Controls**: Pause, resume, end campaigns
- **Performance Analytics**: Detailed campaign reports
- **Lead List Integration**: Connect to imported lead lists

**Active Campaigns**:
- Q2 Lead Reactivation (3,500/5,000 leads called, 48 appointments)

**Completed Campaigns**:
- January Win-Back (850 leads, 22 appointments)
- Spring Promotion (1,200 leads, 31 appointments)

---

### 4. Analytics (`/analytics`)
**Purpose**: Detailed call logs and performance analysis

**Key Features**:
- **Call Log Table**: Comprehensive call history
- **Performance Metrics**: 
  - Total Calls Today (127)
  - Appointments Booked (23)
  - Average Call Duration (3:24)
  - Conversion Rate (18.1%)
- **Advanced Filtering**: By agent, outcome, date range
- **Call Outcomes**: Booked, Interested, Transferred, No Answer, Disqualified
- **Audio Playback**: Review recorded calls
- **Export Functionality**: Generate reports
- **Real-time Updates**: Live call monitoring

**Call Log Details**:
- Timestamp, Lead Name, Phone Number
- Agent Used, Call Duration
- Outcome with color-coded badges
- Playback and transcript access

---

### 5. Leads (`/leads`)
**Purpose**: Comprehensive lead and contact management

**Key Features**:
- **Dual View System**:
  - Lead Lists: Manage imported contact lists
  - All Contacts: Individual contact management
- **Lead List Management**:
  - Import/Export CSV files
  - Track list usage in campaigns
  - List status monitoring (Used, Ready, Active)
- **Contact Management**:
  - Individual contact details
  - Call history and status tracking
  - Lead source attribution
- **Search & Filter**: Advanced contact filtering
- **Lead Status Tracking**: Booked, Interested, Callback, No Answer, Disqualified

**Sample Lead Lists**:
- Old Roof Leads (5,000 contacts, used in 1 campaign)
- Unclosed Deals 2024 (850 contacts, used in 2 campaigns)
- Summer_Leads_2025.csv (1,245 contacts, ready)
- New Website Leads (43 contacts, active/dynamic)

---

### 6. Settings (`/settings`)
**Purpose**: Account management and system configuration

**Key Features**:
- **User Profile Management**:
  - Business Name, Owner Name
  - Contact Information
  - Account Settings
- **Billing & Subscription**:
  - Professional Plan ($297/month)
  - Payment Method Management
  - Billing History
- **Phone Number Management**:
  - Multiple phone lines
  - Line assignment to agents
  - Number provisioning
- **Integrations**:
  - Google Calendar (Connected)
  - Zapier (Available)
  - Third-party app connections
- **Notification Preferences**:
  - Daily Email Reports
  - SMS Alerts
  - Appointment Reminders

---

## Key Components and Widgets

### AI Orb Widget
**Purpose**: Interactive 3D voice interface for agent testing
**Features**:
- 3D animated orb with React Three Fiber
- Real-time audio visualization
- Microphone input support
- Speaker state indication (thinking, user, agent)
- Volume-responsive animations

### Navigation System
**Purpose**: Consistent sidebar navigation
**Features**:
- Hierarchical menu structure
- Active state indicators
- User profile dropdown
- Business branding display

### Modal System
**Features**:
- Create Agent Dialog (3-step wizard)
- Test Agent Modal (with AI Orb)
- Template Preview Modal
- Lead Detail Modal
- Import List Dialog

---

## Design System

### Color Scheme
- **Background**: Black (`bg-black`)
- **Cards**: Dark gray (`bg-neutral-900`)
- **Borders**: Neutral (`border-neutral-800`)
- **Primary**: Blue gradient (`bg-blue-gradient`)
- **Text**: White primary, neutral secondary

### Component Patterns
- **Cards**: Hover effects with blue accents
- **Buttons**: Gradient primary, outline secondary
- **Badges**: Status-based color coding
- **Tables**: Responsive with hover states
- **Forms**: Dark theme with blue focus states

### Responsive Design
- Mobile-first approach
- Breakpoints: sm, md, lg, xl
- Collapsible sidebar for mobile
- Adaptive grid layouts

---

## Technical Architecture

### Framework Stack
- **Next.js 14**: App Router, Server Components
- **TypeScript**: Full type safety
- **Tailwind CSS**: Utility-first styling
- **React Three Fiber**: 3D graphics for AI Orb
- **Lucide React**: Icon system
- **Radix UI**: Accessible component primitives

### State Management
- React useState for local state
- Component-level state management
- Props drilling for data flow

### Audio Features
- Real-time audio processing
- Microphone input capture
- Volume visualization
- Speaker state management

This comprehensive platform provides end-to-end management of AI voice agents and advertising campaigns, with sophisticated analytics and lead management capabilities.
