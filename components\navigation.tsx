"use client"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { LayoutDashboard, Bot, Megaphone, BarChart3, Users, Settings, ChevronDown, User, FileText } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface NavigationProps {
  activeTab: string
}

export function Navigation({ activeTab }: NavigationProps) {
  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-black border-r border-neutral-800 flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-neutral-800">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-blue-gradient rounded-lg flex items-center justify-center glow-blue">
            <Bot className="h-5 w-5 text-white" />
          </div>
          <span className="ml-3 text-base font-medium text-white">AI VoiceConnect</span>
        </div>
      </div>

      {/* Navigation Links */}
      <nav className="flex-1 p-3">
        <div className="space-y-1">
          {/* Dashboard */}
          <Link
            href="/"
            className={cn(
              "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
              activeTab === "dashboard"
                ? "bg-blue-gradient text-white shadow-lg glow-blue"
                : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
            )}
          >
            <LayoutDashboard className="h-4 w-4 mr-3" />
            Dashboard
            {activeTab === "dashboard" && <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />}
          </Link>

          {/* Ads Section */}
          <div className="pt-6">
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-blue-400 uppercase tracking-wider">Ads</h3>
            </div>
            <Link
              href="/ads"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "ads-library"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <FileText className="h-4 w-4 mr-3" />
              Ad Library
              {activeTab === "ads-library" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
            <Link
              href="/ads/templates"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "my-templates"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <FileText className="h-4 w-4 mr-3" />
              My Templates
              {activeTab === "my-templates" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
            <Link
              href="/ads/forms"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "lead-forms"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <FileText className="h-4 w-4 mr-3" />
              Lead Forms
              {activeTab === "lead-forms" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
            <Link
              href="/ads/campaigns"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "ads-campaigns"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <Megaphone className="h-4 w-4 mr-3" />
              Ad Campaigns
              {activeTab === "ads-campaigns" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
          </div>

          {/* Agents Section */}
          <div className="pt-6">
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-blue-400 uppercase tracking-wider">Voice Agents</h3>
            </div>
            <Link
              href="/agents"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "agents"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <Bot className="h-4 w-4 mr-3" />
              Agents
              {activeTab === "agents" && <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />}
            </Link>
            <Link
              href="/campaigns"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "campaigns"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <Megaphone className="h-4 w-4 mr-3" />
              Voice Campaigns
              {activeTab === "campaigns" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
            <Link
              href="/analytics"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "analytics"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <BarChart3 className="h-4 w-4 mr-3" />
              Analytics
              {activeTab === "analytics" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
            <Link
              href="/leads"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "leads"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <Users className="h-4 w-4 mr-3" />
              Leads
              {activeTab === "leads" && <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />}
            </Link>
            <Link
              href="/settings"
              className={cn(
                "flex items-center px-3 py-2.5 rounded-lg text-sm transition-all w-full group relative",
                activeTab === "settings"
                  ? "bg-blue-gradient text-white shadow-lg glow-blue"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-900/50",
              )}
            >
              <Settings className="h-4 w-4 mr-3" />
              Settings
              {activeTab === "settings" && (
                <div className="absolute right-2 w-2 h-2 bg-white rounded-full opacity-80" />
              )}
            </Link>
          </div>
        </div>
      </nav>

      {/* Profile Section */}
      <div className="p-3 border-t border-neutral-800">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start p-3 h-auto hover:bg-neutral-900/50 rounded-lg">
              <div className="flex items-center w-full">
                <div className="w-10 h-10 bg-blue-gradient rounded-full flex items-center justify-center mr-3 glow-blue">
                  <User className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1 text-left">
                  <div className="text-sm font-medium text-white">Miller & Sons</div>
                  <div className="text-xs text-blue-400">Roofing</div>
                </div>
                <ChevronDown className="h-4 w-4 text-neutral-400" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 bg-neutral-900 border-neutral-800">
            <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
              Profile Settings
            </DropdownMenuItem>
            <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
              Billing
            </DropdownMenuItem>
            <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
              Support
            </DropdownMenuItem>
            <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
