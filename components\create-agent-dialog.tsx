"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Phone, Bot, Megaphone, Play } from "lucide-react"

interface CreateAgentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateAgentDialog({ open, onOpenChange }: CreateAgentDialogProps) {
  const [step, setStep] = useState(1)
  const [agentType, setAgentType] = useState("")
  const [agentName, setAgentName] = useState("")
  const [voice, setVoice] = useState("")
  const [greeting, setGreeting] = useState("")

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleCreate = () => {
    console.log("Creating agent:", { agentType, agentName, voice, greeting })
    onOpenChange(false)
    setStep(1)
    setAgentType("")
    setAgentName("")
    setVoice("")
    setGreeting("")
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium text-white">Select Agent Type</Label>
        <p className="text-sm text-neutral-400 mb-4">Choose the type of agent you want to create</p>
        <RadioGroup value={agentType} onValueChange={setAgentType}>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
              <RadioGroupItem value="inbound" id="inbound" />
              <div className="flex items-center space-x-3 flex-1">
                <div className="bg-blue-gradient rounded-full p-1">
                  <Phone className="h-5 w-5 text-white" />
                </div>
                <div>
                  <Label htmlFor="inbound" className="font-medium text-white">
                    Inbound Receptionist
                  </Label>
                  <p className="text-sm text-neutral-400">Handles incoming calls to your business</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
              <RadioGroupItem value="speed-to-lead" id="speed-to-lead" />
              <div className="flex items-center space-x-3 flex-1">
                <div className="bg-blue-gradient rounded-full p-1">
                  <Bot className="h-5 w-5 text-white" />
                </div>
                <div>
                  <Label htmlFor="speed-to-lead" className="font-medium text-white">
                    Outbound - Speed to Lead
                  </Label>
                  <p className="text-sm text-neutral-400">Calls new leads instantly when they submit a form</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
              <RadioGroupItem value="campaign" id="campaign" />
              <div className="flex items-center space-x-3 flex-1">
                <div className="bg-blue-gradient rounded-full p-1">
                  <Megaphone className="h-5 w-5 text-white" />
                </div>
                <div>
                  <Label htmlFor="campaign" className="font-medium text-white">
                    Outbound - Campaign
                  </Label>
                  <p className="text-sm text-neutral-400">Used for calling lists of leads in campaigns</p>
                </div>
              </div>
            </div>
          </div>
        </RadioGroup>
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="agent-name" className="text-white">
          Agent Name
        </Label>
        <Input
          id="agent-name"
          placeholder="e.g., Main Office Line"
          value={agentName}
          onChange={(e) => setAgentName(e.target.value)}
          className="bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
        />
      </div>

      <div>
        <Label htmlFor="voice" className="text-white">
          Voice Selection
        </Label>
        <Select value={voice} onValueChange={setVoice}>
          <SelectTrigger className="bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
            <SelectValue placeholder="Choose a voice" />
          </SelectTrigger>
          <SelectContent className="bg-neutral-900 border-neutral-800">
            <SelectItem value="marissa" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              Marissa (Female, Professional)
            </SelectItem>
            <SelectItem value="david" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              David (Male, Friendly)
            </SelectItem>
            <SelectItem value="sarah" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              Sarah (Female, Warm)
            </SelectItem>
            <SelectItem value="michael" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              Michael (Male, Authoritative)
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <div className="flex items-center justify-between mb-2">
          <Label htmlFor="greeting" className="text-white">
            Greeting & Script
          </Label>
          {voice && (
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Play className="h-4 w-4 mr-1" />
              Test Audio
            </Button>
          )}
        </div>
        <Textarea
          id="greeting"
          placeholder="Hi, this is Julia from Miller & Sons Roofing. How can I help you today?"
          value={greeting}
          onChange={(e) => setGreeting(e.target.value)}
          rows={4}
          className="bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
        />
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium mb-4 text-white">Review Your Agent</h4>
        <div className="space-y-3 p-4 bg-neutral-800 rounded-lg border border-blue-500/20">
          <div>
            <span className="font-medium text-white">Type: </span>
            <span className="capitalize text-neutral-300">{agentType.replace("-", " ")}</span>
          </div>
          <div>
            <span className="font-medium text-white">Name: </span>
            <span className="text-neutral-300">{agentName}</span>
          </div>
          <div>
            <span className="font-medium text-white">Voice: </span>
            <span className="text-neutral-300">{voice}</span>
          </div>
          <div>
            <span className="font-medium text-white">Greeting: </span>
            <span className="text-sm text-neutral-300">{greeting}</span>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-white">Create New Agent (Step {step} of 3)</DialogTitle>
          <DialogDescription className="text-neutral-400">
            {step === 1 && "Choose the type of AI agent you want to create"}
            {step === 2 && "Configure your agent's basic settings"}
            {step === 3 && "Review and create your agent"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </div>

        <DialogFooter className="flex justify-between">
          <div>
            {step > 1 && (
              <Button
                variant="outline"
                onClick={handleBack}
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                Back
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              Cancel
            </Button>
            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={step === 1 && !agentType}
                className="bg-blue-gradient-hover text-white shadow-lg"
              >
                Next
              </Button>
            ) : (
              <Button onClick={handleCreate} className="bg-blue-gradient-hover text-white shadow-lg">
                Create Agent
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
