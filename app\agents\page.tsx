"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, Bot, Phone, Megaphone, Play, Pause, BarChart3, Edit, Mic } from "lucide-react"
import { Navigation } from "@/components/navigation"
import { CreateAgentDialog } from "@/components/create-agent-dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { TestAgentModal } from "@/components/test-agent-modal" // Import the new modal

const agents = [
  {
    id: 1,
    name: "Main Office Line",
    type: "Inbound Receptionist",
    phoneNumber: "(*************",
    status: "active",
    description: "Handles incoming calls during business hours",
  },
  {
    id: 2,
    name: "Speed-to-Lead Agent",
    type: "Outbound (New Leads)",
    trigger: "New lead from 'Facebook Ads' form",
    status: "active",
    description: "Instantly calls new leads from web forms",
  },
  {
    id: 3,
    name: "Trash to Cash Agent",
    type: "Outbound (Campaign)",
    script: "Q2 Promo Script",
    status: "inactive",
    description: "Used for calling old lead lists with promotions",
  },
]

const getTypeIcon = (type: string) => {
  if (type.includes("Inbound")) return Phone
  if (type.includes("Campaign")) return Megaphone
  return Bot
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Active</Badge>
    case "inactive":
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Inactive</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
  }
}

export default function AgentsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showTestAgentDialog, setShowTestAgentDialog] = useState(false) // New state for test modal
  const [selectedAgentNameForTest, setSelectedAgentNameForTest] = useState("") // New state for agent name

  const filteredAgents = agents.filter((agent) => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === "all" || agent.type.toLowerCase().includes(typeFilter.toLowerCase())
    const matchesStatus = statusFilter === "all" || agent.status === statusFilter
    return matchesSearch && matchesType && matchesStatus
  })

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="agents" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Agents</h1>
            <p className="text-neutral-400 mt-1 text-sm">Create and manage your AI voice agents</p>
          </div>
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="flex items-center gap-2 bg-blue-gradient-hover text-white shadow-lg"
          >
            <Plus className="h-4 w-4" />
            Create New Agent
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 h-4 w-4" />
            <Input
              placeholder="Search agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
            />
          </div>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent className="bg-neutral-900 border-neutral-800">
              <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                All Types
              </SelectItem>
              <SelectItem value="inbound" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Inbound
              </SelectItem>
              <SelectItem value="outbound" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Outbound
              </SelectItem>
              <SelectItem value="campaign" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Campaign
              </SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent className="bg-neutral-900 border-neutral-800">
              <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                All Status
              </SelectItem>
              <SelectItem value="active" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Active
              </SelectItem>
              <SelectItem value="inactive" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Inactive
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Agents Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAgents.map((agent) => {
            const TypeIcon = getTypeIcon(agent.type)
            return (
              <Card key={agent.id} className="bg-neutral-900 border-neutral-800 card-hover-blue">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                        <TypeIcon className="h-4 w-4 text-neutral-400" />
                      </div>
                      <div>
                        <CardTitle className="text-base font-medium text-white">{agent.name}</CardTitle>
                        <p className="text-sm text-neutral-500">{agent.type}</p>
                      </div>
                    </div>
                    {getStatusBadge(agent.status)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-neutral-400">{agent.description}</p>

                  {agent.phoneNumber && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-neutral-500" />
                      <span className="text-neutral-400">{agent.phoneNumber}</span>
                    </div>
                  )}

                  {agent.trigger && (
                    <div className="text-sm">
                      <span className="font-medium text-white">Trigger: </span>
                      <span className="text-neutral-400">{agent.trigger}</span>
                    </div>
                  )}

                  {agent.script && (
                    <div className="text-sm">
                      <span className="font-medium text-white">Script: </span>
                      <span className="text-neutral-400">{agent.script}</span>
                    </div>
                  )}

                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    >
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Analytics
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                        >
                          {agent.status === "active" ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-neutral-900 border-neutral-800">
                        <DropdownMenuItem
                          className="text-neutral-300 hover:text-white hover:bg-neutral-800"
                          onClick={() => {
                            setShowTestAgentDialog(true)
                            setSelectedAgentNameForTest(agent.name)
                          }}
                        >
                          <Mic className="h-4 w-4 mr-2" />
                          Test Agent
                        </DropdownMenuItem>
                        {agent.status === "active" ? (
                          <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
                            <Pause className="h-4 w-4 mr-2" />
                            Pause Agent
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
                            <Play className="h-4 w-4 mr-2" />
                            Activate Agent
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {filteredAgents.length === 0 && (
          <div className="text-center py-12">
            <Bot className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No agents found</h3>
            <p className="text-neutral-400 mb-4">
              {searchTerm || typeFilter !== "all" || statusFilter !== "all"
                ? "Try adjusting your search or filters"
                : "Get started by creating your first AI agent"}
            </p>
            {!searchTerm && typeFilter === "all" && statusFilter === "all" && (
              <Button onClick={() => setShowCreateDialog(true)} className="bg-blue-gradient-hover text-white shadow-lg">
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Agent
              </Button>
            )}
          </div>
        )}
      </main>

      <CreateAgentDialog open={showCreateDialog} onOpenChange={setShowCreateDialog} />
      <TestAgentModal
        open={showTestAgentDialog}
        onOpenChange={setShowTestAgentDialog}
        agentName={selectedAgentNameForTest}
      />
    </div>
  )
}
