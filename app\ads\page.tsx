"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Search, Grid, List, Plus, LayoutGrid, FileText, Download, Heart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Navigation } from "@/components/navigation"

const adTemplates = [
  {
    id: 1,
    title: "Spring Clean-Up Package Special",
    category: "Seasonal",
    likes: 342,
    downloads: 156,
    image: "/placeholder.svg?height=350&width=300",
    tags: ["spring", "house-wash", "driveway", "bundle"],
    description: "House Wash + Driveway Cleaning + Gutter Clean-Out",
  },
  {
    id: 2,
    title: "Before & After: Roof Soft Washing",
    category: "Before/After",
    likes: 289,
    downloads: 134,
    image: "/placeholder.svg?height=400&width=300",
    tags: ["roof", "algae-removal", "soft-wash", "transformation"],
    description: "Safe removal of algae, moss & lichen from shingles",
  },
  {
    id: 3,
    title: "Commercial Fleet Washing Service",
    category: "Commercial",
    likes: 198,
    downloads: 89,
    image: "/placeholder.svg?height=320&width=300",
    tags: ["fleet", "commercial", "trucks", "on-location"],
    description: "Professional on-location fleet washing services",
  },
  {
    id: 4,
    title: "Neighborhood Group Discount",
    category: "Promotional",
    likes: 456,
    downloads: 234,
    image: "/placeholder.svg?height=360&width=300",
    tags: ["group-discount", "neighbors", "community", "savings"],
    description: "Book with 3+ neighbors and everyone saves 20%!",
  },
  {
    id: 5,
    title: "House Washing: All Siding Types",
    category: "Residential",
    likes: 378,
    downloads: 167,
    image: "/placeholder.svg?height=340&width=300",
    tags: ["house-wash", "vinyl", "stucco", "brick", "soft-wash"],
    description: "Safe soft washing for vinyl, stucco, wood & brick",
  },
  {
    id: 6,
    title: "Dumpster Pad Sanitization",
    category: "Commercial",
    likes: 145,
    downloads: 67,
    image: "/placeholder.svg?height=300&width=300",
    tags: ["sanitization", "hot-water", "commercial", "odor-removal"],
    description: "Hot water cleaning to eliminate odors & pests",
  },
]

export default function AdLibraryPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [viewMode, setViewMode] = useState("grid")
  // Add state for import success
  const [importedTemplate, setImportedTemplate] = useState<number | null>(null)

  const categories = [
    "all",
    "Residential",
    "Commercial",
    "Seasonal",
    "Promotional",
    "Before/After",
    "Guarantee",
    "Holiday",
    "Eco-Friendly",
    "Specialized",
    "Referral",
    "Subscription",
    "Upsell",
  ]

  const filteredTemplates = adTemplates.filter((template) => {
    const matchesSearch =
      template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // Add import handler
  const handleImportTemplate = (templateId: number) => {
    // In a real app, this would make an API call to import the template
    console.log("Importing template:", templateId)
    setImportedTemplate(templateId)

    // Show success message briefly
    setTimeout(() => {
      setImportedTemplate(null)
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="ads-library" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Ad Library</h1>
            <p className="text-neutral-400 mt-1 text-sm">Browse and customize professional ad templates</p>
          </div>
          <Button className="bg-blue-gradient-hover text-white shadow-lg">
            <Plus className="h-4 w-4 mr-2" />
            Create New Ad
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-16 h-16 bg-blue-gradient opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Templates</p>
                  <p className="text-2xl font-medium text-white">{adTemplates.length}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <LayoutGrid className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-16 h-16 bg-blue-gradient opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Downloads</p>
                  <p className="text-2xl font-medium text-white">1.2K</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <Download className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-16 h-16 bg-blue-gradient opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Categories</p>
                  <p className="text-2xl font-medium text-white">{categories.length - 1}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <FileText className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-16 h-16 bg-blue-gradient opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Favorites</p>
                  <p className="text-2xl font-medium text-white">24</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <Heart className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-neutral-900 border-neutral-800 mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48 bg-neutral-800 border-neutral-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-900 border-neutral-800">
                    {categories.map((category) => (
                      <SelectItem
                        key={category}
                        value={category}
                        className="text-neutral-300 hover:text-white hover:bg-neutral-800"
                      >
                        {category === "all" ? "All Categories" : category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={
                    viewMode === "grid"
                      ? "bg-white text-black hover:bg-neutral-200"
                      : "border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
                  }
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={
                    viewMode === "list"
                      ? "bg-white text-black hover:bg-neutral-200"
                      : "border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
                  }
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTemplates.map((template) => (
            <Card
              key={template.id}
              className="bg-neutral-900 border-neutral-800 hover:border-neutral-700 transition-colors overflow-hidden"
            >
              <div className="relative">
                <img
                  src={template.image || "/placeholder.svg"}
                  alt={template.title}
                  className="w-full h-48 object-cover"
                />
                <Badge className="absolute top-3 left-3 bg-blue-500/10 text-blue-400 border-blue-500/20">
                  {template.category}
                </Badge>
              </div>

              <CardContent className="p-4">
                <h3 className="font-medium text-white mb-2 line-clamp-2 text-sm leading-5">{template.title}</h3>

                <div className="flex flex-wrap gap-1 mb-3">
                  {template.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} className="text-xs bg-neutral-800 text-neutral-400 border-neutral-700">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <p className="text-sm text-neutral-400 mb-4 line-clamp-2 leading-5">{template.description}</p>

                <div className="flex gap-2 mb-3">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleImportTemplate(template.id)}
                    disabled={importedTemplate === template.id}
                    className="flex-1 border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40 text-xs"
                  >
                    {importedTemplate === template.id ? "Imported!" : "Import"}
                  </Button>
                  <Button
                    size="sm"
                    className="flex-1 bg-blue-gradient-hover text-white text-xs"
                    onClick={() => router.push(`/ads/templates/${template.id}/preview?from=library`)}
                  >
                    Preview
                  </Button>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-neutral-800">
                  <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3 text-neutral-500" />
                    <span className="text-xs text-neutral-500">{template.likes}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Download className="h-3 w-3 text-neutral-500" />
                    <span className="text-xs text-neutral-500">{template.downloads}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No templates found</h3>
            <p className="text-neutral-400 mb-4">Try adjusting your search or filters</p>
            <Button
              variant="outline"
              className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
              onClick={() => {
                setSearchTerm("")
                setSelectedCategory("all")
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </main>
    </div>
  )
}
