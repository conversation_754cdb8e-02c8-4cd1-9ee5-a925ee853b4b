"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Droplets, Building, MapPin, Upload, Facebook, Chrome, CreditCard } from "lucide-react"

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [businessData, setBusinessData] = useState({
    businessName: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    phone: "",
    website: "",
    serviceRadius: "15",
    logo: null as File | null
  })

  const totalSteps = 4
  const progress = (currentStep / totalSteps) * 100

  const handleInputChange = (field: string, value: string) => {
    setBusinessData(prev => ({ ...prev, [field]: value }))
  }

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setBusinessData(prev => ({ ...prev, logo: file }))
    }
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Tell us about your business</h2>
        <p className="text-gray-600">We'll use this information to customize your campaigns</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="businessName">Business Name *</Label>
          <Input
            id="businessName"
            placeholder="e.g., Crystal Clean Pressure Washing"
            value={businessData.businessName}
            onChange={(e) => handleInputChange("businessName", e.target.value)}
            className="h-11"
          />
        </div>

        <div>
          <Label htmlFor="phone">Business Phone *</Label>
          <Input
            id="phone"
            type="tel"
            placeholder="(*************"
            value={businessData.phone}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            className="h-11"
          />
        </div>

        <div>
          <Label htmlFor="website">Website (Optional)</Label>
          <Input
            id="website"
            placeholder="www.yourwebsite.com"
            value={businessData.website}
            onChange={(e) => handleInputChange("website", e.target.value)}
            className="h-11"
          />
        </div>

        <div>
          <Label htmlFor="logo">Business Logo (Optional)</Label>
          <div className="mt-2">
            <label htmlFor="logo-upload" className="cursor-pointer">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  {businessData.logo ? businessData.logo.name : "Click to upload your logo"}
                </p>
                <p className="text-xs text-gray-400 mt-1">PNG, JPG up to 2MB</p>
              </div>
            </label>
            <input
              id="logo-upload"
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Where do you operate?</h2>
        <p className="text-gray-600">This helps us target your ads to the right customers</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="address">Business Address *</Label>
          <Input
            id="address"
            placeholder="123 Main Street"
            value={businessData.address}
            onChange={(e) => handleInputChange("address", e.target.value)}
            className="h-11"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="city">City *</Label>
            <Input
              id="city"
              placeholder="Your City"
              value={businessData.city}
              onChange={(e) => handleInputChange("city", e.target.value)}
              className="h-11"
            />
          </div>
          <div>
            <Label htmlFor="state">State *</Label>
            <Select value={businessData.state} onValueChange={(value) => handleInputChange("state", value)}>
              <SelectTrigger className="h-11">
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="AL">Alabama</SelectItem>
                <SelectItem value="CA">California</SelectItem>
                <SelectItem value="FL">Florida</SelectItem>
                <SelectItem value="TX">Texas</SelectItem>
                {/* Add more states as needed */}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="zipCode">ZIP Code *</Label>
          <Input
            id="zipCode"
            placeholder="12345"
            value={businessData.zipCode}
            onChange={(e) => handleInputChange("zipCode", e.target.value)}
            className="h-11"
          />
        </div>

        <div>
          <Label htmlFor="serviceRadius">Service Radius (miles) *</Label>
          <Select value={businessData.serviceRadius} onValueChange={(value) => handleInputChange("serviceRadius", value)}>
            <SelectTrigger className="h-11">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 miles</SelectItem>
              <SelectItem value="10">10 miles</SelectItem>
              <SelectItem value="15">15 miles</SelectItem>
              <SelectItem value="25">25 miles</SelectItem>
              <SelectItem value="50">50 miles</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Connect your ad accounts</h2>
        <p className="text-gray-600">We'll launch campaigns directly in your accounts</p>
      </div>

      <div className="space-y-4">
        <Card className="border-2 border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Facebook className="h-8 w-8 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-gray-900">Facebook & Instagram Ads</h3>
                  <p className="text-sm text-gray-600">Required for lead generation campaigns</p>
                </div>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700">
                Connect Facebook
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="border-2 border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Chrome className="h-8 w-8 text-gray-600" />
                <div>
                  <h3 className="font-semibold text-gray-900">Google Ads</h3>
                  <p className="text-sm text-gray-600">Optional - for search campaigns</p>
                </div>
              </div>
              <Button variant="outline">
                Connect Google
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-sm text-yellow-800">
            <strong>Important:</strong> You'll pay for ads directly through your connected accounts. 
            PressureMax only charges for the software subscription.
          </p>
        </div>
      </div>
    </div>
  )

  const renderStep4 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Choose your plan</h2>
        <p className="text-gray-600">Start with a 14-day free trial, cancel anytime</p>
      </div>

      <div className="space-y-4">
        <Card className="border-2 border-blue-500 bg-blue-50">
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900">Professional</h3>
              <div className="mt-2">
                <span className="text-4xl font-bold text-blue-600">$97</span>
                <span className="text-gray-600">/month</span>
              </div>
              <p className="text-sm text-gray-600 mt-2">Everything you need to grow</p>
            </div>
            
            <div className="mt-6 space-y-3">
              <div className="flex items-center text-sm">
                <span className="text-green-500 mr-2">✓</span>
                Unlimited ad campaigns
              </div>
              <div className="flex items-center text-sm">
                <span className="text-green-500 mr-2">✓</span>
                Built-in CRM & lead tracking
              </div>
              <div className="flex items-center text-sm">
                <span className="text-green-500 mr-2">✓</span>
                15+ proven ad templates
              </div>
              <div className="flex items-center text-sm">
                <span className="text-green-500 mr-2">✓</span>
                SMS & email notifications
              </div>
              <div className="flex items-center text-sm">
                <span className="text-green-500 mr-2">✓</span>
                24/7 support
              </div>
            </div>

            <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700">
              <CreditCard className="h-4 w-4 mr-2" />
              Start Free Trial
            </Button>
          </CardContent>
        </Card>

        <p className="text-xs text-gray-500 text-center">
          Your trial starts immediately. No charges for 14 days.
        </p>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-blue-600 rounded-full p-3">
              <Droplets className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">PressureMax</h1>
          <p className="text-gray-600 mt-2">Let's get your business set up for success</p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Main Content */}
        <Card className="shadow-xl border-0">
          <CardContent className="p-8">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}

            {/* Navigation */}
            <div className="flex justify-between mt-8 pt-6 border-t">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1}
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={currentStep === totalSteps}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {currentStep === totalSteps ? "Complete Setup" : "Continue"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
