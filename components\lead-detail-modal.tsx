"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { User, Phone, Mail, Calendar, Play, Plus, Edit, Clock, Bot, Megaphone } from "lucide-react"

interface Lead {
  id: number
  name: string
  phone: string
  email: string
  lastCalled: string
  status: string
  lists: string[]
}

interface LeadDetailModalProps {
  lead: Lead | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

const activityHistory = [
  {
    id: 1,
    date: "July 15, 2025",
    agent: "Q2 Lead Reactivation",
    outcome: "Appt. Booked",
    hasRecording: true,
    type: "campaign",
  },
  {
    id: 2,
    date: "July 14, 2025",
    agent: "Q2 Lead Reactivation",
    outcome: "No Answer",
    hasRecording: false,
    type: "campaign",
  },
  {
    id: 3,
    date: "June 5, 2025",
    agent: "Initial Website Inquiry",
    outcome: "Called, no answer",
    hasRecording: true,
    type: "manual",
  },
]

const notes = [
  {
    id: 1,
    text: "Called back manually on July 16th, confirmed appointment for Friday.",
    author: "Frank M.",
    date: "July 16, 2025",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "booked":
      return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Appointment Booked</Badge>
    case "interested":
      return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Interested</Badge>
    case "callback":
      return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Callback</Badge>
    case "no-answer":
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">No Answer</Badge>
    case "disqualified":
      return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Disqualified</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
  }
}

const getOutcomeBadge = (outcome: string) => {
  if (outcome.toLowerCase().includes("booked") || outcome.toLowerCase().includes("appointment")) {
    return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">{outcome}</Badge>
  }
  if (outcome.toLowerCase().includes("interested")) {
    return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">{outcome}</Badge>
  }
  if (outcome.toLowerCase().includes("no answer")) {
    return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{outcome}</Badge>
  }
  return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{outcome}</Badge>
}

export function LeadDetailModal({ lead, open, onOpenChange }: LeadDetailModalProps) {
  const [newNote, setNewNote] = useState("")

  if (!lead) return null

  const handleAddNote = () => {
    if (newNote.trim()) {
      console.log("Adding note:", newNote)
      setNewNote("")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white">Lead Details: {lead.name}</DialogTitle>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit Contact
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Contact Information */}
          <Card className="bg-neutral-800 border-neutral-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base font-medium text-white">
                <User className="h-4 w-4" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-neutral-400">Name</Label>
                  <p className="text-white">{lead.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-neutral-400">Status</Label>
                  <div className="mt-1">{getStatusBadge(lead.status)}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-neutral-400">Phone</Label>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-neutral-500" />
                    <p className="text-white">{lead.phone}</p>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-neutral-400">Email</Label>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-neutral-500" />
                    <p className="text-white">{lead.email}</p>
                  </div>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-neutral-400">Lists</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {lead.lists.map((list, index) => (
                    <Badge key={index} className="bg-neutral-700 text-neutral-300 border-neutral-600">
                      {list}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activity History */}
          <Card className="bg-neutral-800 border-neutral-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base font-medium text-white">
                <Clock className="h-4 w-4" />
                Activity History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-neutral-700">
                      <th className="text-left py-2 px-3 font-medium text-neutral-400 text-sm">Date</th>
                      <th className="text-left py-2 px-3 font-medium text-neutral-400 text-sm">Agent/Campaign</th>
                      <th className="text-left py-2 px-3 font-medium text-neutral-400 text-sm">Outcome</th>
                      <th className="text-left py-2 px-3 font-medium text-neutral-400 text-sm">Recording</th>
                    </tr>
                  </thead>
                  <tbody>
                    {activityHistory.map((activity) => (
                      <tr key={activity.id} className="border-b border-neutral-700">
                        <td className="py-2 px-3 text-sm text-white">{activity.date}</td>
                        <td className="py-2 px-3">
                          <div className="flex items-center gap-2">
                            {activity.type === "campaign" ? (
                              <Megaphone className="h-4 w-4 text-purple-400" />
                            ) : (
                              <Bot className="h-4 w-4 text-blue-400" />
                            )}
                            <span className="text-sm text-white">{activity.agent}</span>
                          </div>
                        </td>
                        <td className="py-2 px-3">{getOutcomeBadge(activity.outcome)}</td>
                        <td className="py-2 px-3">
                          {activity.hasRecording ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                          ) : (
                            <span className="text-neutral-500 text-sm">--</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card className="bg-neutral-800 border-neutral-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-base font-medium text-white">
                  <Calendar className="h-4 w-4" />
                  Notes
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddNote}
                  disabled={!newNote.trim()}
                  className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Note
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="Add a note about this lead..."
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                className="bg-neutral-900 border-neutral-700 text-white placeholder:text-neutral-500"
                rows={3}
              />

              <div className="space-y-3">
                {notes.map((note) => (
                  <div key={note.id} className="p-3 bg-neutral-900 rounded-lg border border-neutral-700">
                    <p className="text-sm text-white mb-2">{note.text}</p>
                    <p className="text-xs text-neutral-500">
                      - {note.author}, {note.date}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
