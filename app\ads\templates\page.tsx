"use client"

import { useState } from "react"
import { Search, Grid, List, Plus, Edit, Copy, Trash2, Download, Heart, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Navigation } from "@/components/navigation"
import { TemplatePreviewModal } from "@/components/template-preview-modal"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const myTemplates = [
  {
    id: 1,
    title: "Spring Clean-Up Package Special - Custom",
    category: "Seasonal",
    status: "Draft",
    lastModified: "2025-07-18",
    campaigns: 2,
    image: "/placeholder.svg?height=350&width=300",
    tags: ["spring", "house-wash", "driveway", "bundle"],
    description: "House Wash + Driveway Cleaning + Gutter Clean-Out - Customized for Miller & Sons",
    isCustom: true,
  },
  {
    id: 2,
    title: "Roof Cleaning Before/After - Miller & Sons",
    category: "Before/After",
    status: "Active",
    lastModified: "2025-07-15",
    campaigns: 1,
    image: "/placeholder.svg?height=400&width=300",
    tags: ["roof", "algae-removal", "soft-wash", "transformation"],
    description: "Safe removal of algae, moss & lichen from shingles - Custom branded",
    isCustom: true,
  },
  {
    id: 3,
    title: "Commercial Fleet Washing Service",
    category: "Commercial",
    status: "Draft",
    lastModified: "2025-07-10",
    campaigns: 0,
    image: "/placeholder.svg?height=320&width=300",
    tags: ["fleet", "commercial", "trucks", "on-location"],
    description: "Professional on-location fleet washing services",
    isCustom: false,
  },
]

export default function MyTemplatesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [viewMode, setViewMode] = useState("grid")
  const [previewTemplate, setPreviewTemplate] = useState<any>(null)

  const categories = ["all", "Residential", "Commercial", "Seasonal", "Promotional", "Before/After", "Custom"]

  const statuses = ["all", "Draft", "Active", "Paused", "Archived"]

  const filteredTemplates = myTemplates.filter((template) => {
    const matchesSearch =
      template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory
    const matchesStatus = selectedStatus === "all" || template.status === selectedStatus
    return matchesSearch && matchesCategory && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Active</Badge>
      case "Draft":
        return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Draft</Badge>
      case "Paused":
        return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Paused</Badge>
      case "Archived":
        return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Archived</Badge>
      default:
        return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="my-templates" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">My Templates</h1>
            <p className="text-neutral-400 mt-1 text-sm">Manage your custom ad templates and imported designs</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              <Plus className="h-4 w-4 mr-2" />
              Import from Library
            </Button>
            <Button className="bg-blue-gradient-hover text-white shadow-lg">
              <Plus className="h-4 w-4 mr-2" />
              Create New Template
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Templates</p>
                  <p className="text-2xl font-medium text-white">{myTemplates.length}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Eye className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Active Templates</p>
                  <p className="text-2xl font-medium text-white">
                    {myTemplates.filter((t) => t.status === "Active").length}
                  </p>
                </div>
                <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Active</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Draft Templates</p>
                  <p className="text-2xl font-medium text-white">
                    {myTemplates.filter((t) => t.status === "Draft").length}
                  </p>
                </div>
                <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Draft</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Campaigns</p>
                  <p className="text-2xl font-medium text-white">
                    {myTemplates.reduce((sum, t) => sum + t.campaigns, 0)}
                  </p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Heart className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-neutral-900 border-neutral-800 mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                  />
                </div>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48 bg-neutral-800 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-900 border-neutral-800">
                    {categories.map((category) => (
                      <SelectItem
                        key={category}
                        value={category}
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        {category === "all" ? "All Categories" : category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-48 bg-neutral-800 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-900 border-neutral-800">
                    {statuses.map((status) => (
                      <SelectItem
                        key={status}
                        value={status}
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        {status === "all" ? "All Status" : status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={
                    viewMode === "grid"
                      ? "bg-blue-gradient-hover text-white"
                      : "border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  }
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={
                    viewMode === "list"
                      ? "bg-blue-gradient-hover text-white"
                      : "border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  }
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="bg-neutral-900 border-neutral-800 card-hover-blue overflow-hidden">
              <div className="relative">
                <img
                  src={template.image || "/placeholder.svg"}
                  alt={template.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 flex gap-2">
                  <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">{template.category}</Badge>
                  {template.isCustom && (
                    <Badge className="bg-purple-500/10 text-purple-400 border-purple-500/20">Custom</Badge>
                  )}
                </div>
                <div className="absolute top-3 right-3">{getStatusBadge(template.status)}</div>
              </div>

              <CardContent className="p-4">
                <h3 className="font-medium text-white mb-2 line-clamp-2 text-sm leading-5">{template.title}</h3>

                <div className="flex flex-wrap gap-1 mb-3">
                  {template.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} className="text-xs bg-neutral-800 text-neutral-400 border-neutral-700">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <p className="text-sm text-neutral-400 mb-4 line-clamp-2 leading-5">{template.description}</p>

                <div className="space-y-3">
                  <div className="flex items-center justify-between text-xs text-neutral-500">
                    <span>Modified: {template.lastModified}</span>
                    <span>{template.campaigns} campaigns</span>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setPreviewTemplate(template)}
                      className="flex-1 border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40 text-xs"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
                        >
                          •••
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-neutral-900 border-neutral-800">
                        <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Template
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-400 hover:text-red-300 hover:bg-neutral-800">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <Button className="w-full bg-blue-gradient-hover text-white text-xs shadow-lg">
                    Launch Campaign
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <Eye className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No templates found</h3>
            <p className="text-neutral-400 mb-4">
              {searchTerm || selectedCategory !== "all" || selectedStatus !== "all"
                ? "Try adjusting your search or filters"
                : "Import templates from the Ad Library or create your own"}
            </p>
            <div className="flex gap-2 justify-center">
              <Button
                variant="outline"
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                Browse Ad Library
              </Button>
              <Button className="bg-blue-gradient-hover text-white shadow-lg">Create New Template</Button>
            </div>
          </div>
        )}

        <TemplatePreviewModal
          isOpen={!!previewTemplate}
          onClose={() => setPreviewTemplate(null)}
          template={previewTemplate || myTemplates[0]}
        />
      </main>
    </div>
  )
}
