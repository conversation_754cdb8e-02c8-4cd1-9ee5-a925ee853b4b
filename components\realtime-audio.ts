// components/RealtimeAudio.ts
export class RealtimeAudio {
  private stream: MediaStream | null = null
  private analyser: AnalyserNode | null = null
  private dataArray: Uint8Array | null = null

  async init() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const ctx = new (window.AudioContext || (window as any).webkitAudioContext)()
      this.analyser = ctx.createAnalyser()
      this.analyser.fftSize = 256
      const source = ctx.createMediaStreamSource(this.stream)
      source.connect(this.analyser)
      this.dataArray = new Uint8Array(this.analyser.frequencyBinCount)
    } catch (error) {
      console.error("Error initializing audio:", error)
      // Optionally, you could throw the error or set an error state here
    }
  }

  getVolume(): number {
    if (!this.analyser || !this.dataArray) return 0
    this.analyser.getByteFrequencyData(this.dataArray)
    const sum = this.dataArray.reduce((a, b) => a + b, 0)
    return Math.min(sum / this.dataArray.length / 128, 1)
  }

  close() {
    this.stream?.getTracks().forEach((t) => t.stop())
  }
}
