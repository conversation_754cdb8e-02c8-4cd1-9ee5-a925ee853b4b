"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Plus, Megaphone, Play, Pause, BarChart3 } from "lucide-react"
import { Navigation } from "@/components/navigation"
import { CreateCampaignDialog } from "@/components/create-campaign-dialog"

const activeCampaigns = [
  {
    id: 1,
    name: "Q2 Lead Reactivation",
    leadList: "Old Roof Leads",
    totalLeads: 5000,
    calledLeads: 3500,
    appointmentsBooked: 48,
    agent: "Trash to Cash Agent",
    status: "active",
    startDate: "2025-07-01",
  },
]

const completedCampaigns = [
  {
    id: 2,
    name: "January Win-Back",
    leadList: "Unclosed Deals 2024",
    totalLeads: 850,
    calledLeads: 850,
    appointmentsBooked: 22,
    completedDate: "2025-07-15",
    status: "completed",
  },
  {
    id: 3,
    name: "Spring Promotion",
    leadList: "HVAC Leads 2024",
    totalLeads: 1200,
    calledLeads: 1200,
    appointmentsBooked: 31,
    completedDate: "2025-06-30",
    status: "completed",
  },
]

export default function CampaignsPage() {
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="campaigns" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Campaigns</h1>
            <p className="text-neutral-400 mt-1 text-sm">Manage your outbound calling campaigns</p>
          </div>
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="flex items-center gap-2 bg-blue-gradient-hover text-white shadow-lg"
          >
            <Plus className="h-4 w-4" />
            New Campaign
          </Button>
        </div>

        {/* Active Campaigns */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
            <Play className="h-4 w-4 text-blue-400" />
            Active Campaigns
          </h2>

          {activeCampaigns.length > 0 ? (
            <div className="space-y-4">
              {activeCampaigns.map((campaign) => (
                <Card key={campaign.id} className="bg-neutral-900 border-neutral-800 card-hover-blue">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center gap-2 text-base font-medium text-white">
                          <Megaphone className="h-4 w-4" />
                          {campaign.name}
                        </CardTitle>
                        <p className="text-neutral-400 mt-1 text-sm">
                          Lead List: {campaign.leadList} ({campaign.totalLeads.toLocaleString()})
                        </p>
                      </div>
                      <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Active</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-neutral-400">Progress</span>
                          <span className="font-medium text-white">
                            {Math.round((campaign.calledLeads / campaign.totalLeads) * 100)}%
                          </span>
                        </div>
                        <Progress
                          value={(campaign.calledLeads / campaign.totalLeads) * 100}
                          className="h-2 bg-neutral-800"
                        />
                        <p className="text-xs text-neutral-500">
                          {campaign.calledLeads.toLocaleString()} / {campaign.totalLeads.toLocaleString()} leads called
                        </p>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-medium text-white">{campaign.appointmentsBooked}</div>
                        <p className="text-sm text-neutral-400">Appointments Booked</p>
                      </div>

                      <div className="text-center">
                        <div className="text-base font-medium text-white">{campaign.agent}</div>
                        <p className="text-sm text-neutral-400">Agent Used</p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                      >
                        <Pause className="h-4 w-4 mr-1" />
                        Pause
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                      >
                        <BarChart3 className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                      >
                        End Campaign
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="text-center py-12">
                <Megaphone className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No active campaigns</h3>
                <p className="text-neutral-400 mb-4">Start your first campaign to reactivate old leads</p>
                <Button
                  onClick={() => setShowCreateDialog(true)}
                  className="bg-blue-gradient-hover text-white shadow-lg"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Campaign
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Completed Campaigns */}
        <div>
          <h2 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-neutral-400" />
            Completed Campaigns
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {completedCampaigns.map((campaign) => (
              <Card key={campaign.id} className="bg-neutral-900 border-neutral-800 card-hover-blue">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base font-medium text-white">{campaign.name}</CardTitle>
                  <p className="text-sm text-neutral-400">
                    {campaign.leadList} ({campaign.totalLeads.toLocaleString()})
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-neutral-400">Completed:</span>
                    <span className="text-sm font-medium text-white">{campaign.completedDate}</span>
                  </div>

                  <div className="text-center p-4 bg-neutral-800 rounded-lg">
                    <div className="text-2xl font-medium text-blue-400">{campaign.appointmentsBooked}</div>
                    <p className="text-sm text-neutral-400">Appointments Booked</p>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    View Report
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>

      <CreateCampaignDialog open={showCreateDialog} onOpenChange={setShowCreateDialog} />
    </div>
  )
}
