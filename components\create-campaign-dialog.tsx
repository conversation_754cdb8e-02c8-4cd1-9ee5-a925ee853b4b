"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Upload, FileText, Calendar } from "lucide-react"

interface CreateCampaignDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateCampaignDialog({ open, onOpenChange }: CreateCampaignDialogProps) {
  const [step, setStep] = useState(1)
  const [campaignName, setCampaignName] = useState("")
  const [leadListOption, setLeadListOption] = useState("")
  const [selectedAgent, setSelectedAgent] = useState("")
  const [schedule, setSchedule] = useState("")

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleCreate = () => {
    console.log("Creating campaign:", { campaignName, leadListOption, selectedAgent, schedule })
    onOpenChange(false)
    setStep(1)
    setCampaignName("")
    setLeadListOption("")
    setSelectedAgent("")
    setSchedule("")
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="campaign-name" className="text-white">
          Campaign Name
        </Label>
        <Input
          id="campaign-name"
          placeholder="e.g., Q3 Summer Promo Campaign"
          value={campaignName}
          onChange={(e) => setCampaignName(e.target.value)}
          className="bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
        />
      </div>

      <div>
        <Label className="text-base font-medium text-white">Select Lead List</Label>
        <p className="text-sm text-neutral-400 mb-4">Choose an existing list or upload a new one</p>
        <RadioGroup value={leadListOption} onValueChange={setLeadListOption}>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
              <RadioGroupItem value="existing" id="existing" />
              <div className="flex-1">
                <Label htmlFor="existing" className="font-medium text-white">
                  Use existing list
                </Label>
                {leadListOption === "existing" && (
                  <Select>
                    <SelectTrigger className="mt-2 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                      <SelectValue placeholder="Choose from existing lists" />
                    </SelectTrigger>
                    <SelectContent className="bg-neutral-900 border-neutral-800">
                      <SelectItem
                        value="old-roof-leads"
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        Old Roof Leads (5,000)
                      </SelectItem>
                      <SelectItem
                        value="hvac-leads-2024"
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        HVAC Leads 2024 (1,200)
                      </SelectItem>
                      <SelectItem
                        value="unclosed-deals"
                        className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                      >
                        Unclosed Deals (850)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border border-neutral-800 rounded-lg hover:bg-blue-500/10 transition-colors">
              <RadioGroupItem value="upload" id="upload" />
              <div className="flex items-center space-x-3 flex-1">
                <Upload className="h-5 w-5 text-blue-400" />
                <div>
                  <Label htmlFor="upload" className="font-medium text-white">
                    Upload new CSV
                  </Label>
                  <p className="text-sm text-neutral-400">Upload a CSV file with your leads</p>
                </div>
              </div>
            </div>
          </div>
        </RadioGroup>

        {leadListOption === "upload" && (
          <div className="mt-4 p-4 border-2 border-dashed border-neutral-700 rounded-lg text-center">
            <FileText className="h-8 w-8 text-neutral-500 mx-auto mb-2" />
            <p className="text-sm text-neutral-400 mb-2">Drop your CSV file here or click to browse</p>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              Choose File
            </Button>
          </div>
        )}
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="agent" className="text-white">
          Choose Your Agent
        </Label>
        <Select value={selectedAgent} onValueChange={setSelectedAgent}>
          <SelectTrigger className="bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
            <SelectValue placeholder="Select an agent for this campaign" />
          </SelectTrigger>
          <SelectContent className="bg-neutral-900 border-neutral-800">
            <SelectItem value="trash-to-cash" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              Trash to Cash Agent
            </SelectItem>
            <SelectItem value="promo-agent" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              Promotional Agent
            </SelectItem>
            <SelectItem value="follow-up-agent" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
              Follow-up Agent
            </SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-neutral-400 mt-1">This agent will be used to call all leads in your campaign</p>
      </div>

      <div>
        <Label className="text-base font-medium text-white">Schedule & Cadence</Label>
        <div className="space-y-4 mt-4">
          <div>
            <Label htmlFor="schedule" className="text-white">
              Calling Hours
            </Label>
            <Select value={schedule} onValueChange={setSchedule}>
              <SelectTrigger className="bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                <SelectValue placeholder="When should calls be made?" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-900 border-neutral-800">
                <SelectItem value="business-hours" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  Business Hours (9 AM - 6 PM, Mon-Fri)
                </SelectItem>
                <SelectItem value="extended-hours" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  Extended Hours (8 AM - 8 PM, Mon-Sat)
                </SelectItem>
                <SelectItem value="custom" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  Custom Schedule
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="cadence" className="text-white">
              Call Attempts
            </Label>
            <Select>
              <SelectTrigger className="bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                <SelectValue placeholder="How many times to attempt each lead?" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-900 border-neutral-800">
                <SelectItem value="1" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  1 attempt per lead
                </SelectItem>
                <SelectItem value="2" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  2 attempts per lead
                </SelectItem>
                <SelectItem value="3" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  3 attempts per lead (recommended)
                </SelectItem>
                <SelectItem value="5" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                  5 attempts per lead
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium mb-4 text-white">Review & Launch</h4>
        <div className="space-y-4 p-4 bg-neutral-800 rounded-lg border border-blue-500/20">
          <div className="flex justify-between">
            <span className="font-medium text-white">Campaign Name:</span>
            <span className="text-neutral-300">{campaignName}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-white">Lead List:</span>
            <span className="text-neutral-300">
              {leadListOption === "existing" ? "Old Roof Leads (5,000)" : "Uploaded CSV"}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-white">Agent:</span>
            <span className="text-neutral-300">{selectedAgent}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-white">Schedule:</span>
            <span className="text-neutral-300">{schedule}</span>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="h-4 w-4 text-blue-400" />
            <span className="font-medium text-blue-400">Ready to Launch</span>
          </div>
          <p className="text-sm text-blue-300">
            Your campaign will start immediately after creation. You can pause it at any time from the campaigns
            dashboard.
          </p>
        </div>
      </div>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-white">Create New Campaign (Step {step} of 3)</DialogTitle>
          <DialogDescription className="text-neutral-400">
            {step === 1 && "Set up your campaign name and select your lead list"}
            {step === 2 && "Choose your agent and configure the calling schedule"}
            {step === 3 && "Review your campaign settings and launch"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </div>

        <DialogFooter className="flex justify-between">
          <div>
            {step > 1 && (
              <Button
                variant="outline"
                onClick={handleBack}
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                Back
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
            >
              Cancel
            </Button>
            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={step === 1 && (!campaignName || !leadListOption)}
                className="bg-blue-gradient-hover text-white shadow-lg"
              >
                Next: {step === 1 ? "Schedule" : "Review"}
              </Button>
            ) : (
              <Button onClick={handleCreate} className="bg-blue-gradient-hover text-white shadow-lg">
                Launch Campaign
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
