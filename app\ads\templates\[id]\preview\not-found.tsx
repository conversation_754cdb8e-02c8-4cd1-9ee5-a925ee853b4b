import Link from "next/link"
import { <PERSON>Lef<PERSON>, FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Navigation } from "@/components/navigation"

export default function TemplatePreviewNotFound() {
  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="ads-library" />

      <main className="ml-64">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <div className="flex items-center gap-4">
            <Link href="/ads">
              <Button
                variant="ghost"
                size="sm"
                className="text-neutral-400 hover:text-white hover:bg-neutral-800"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Ad Library
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-medium text-white">Template Not Found</h1>
              <p className="text-neutral-400 mt-1">The requested template could not be found</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex items-center justify-center h-[calc(100vh-140px)]">
          <div className="text-center">
            <FileText className="h-16 w-16 text-neutral-600 mx-auto mb-6" />
            <h2 className="text-xl font-medium text-white mb-2">Template Not Found</h2>
            <p className="text-neutral-400 mb-6 max-w-md">
              The template you're looking for doesn't exist or may have been removed.
            </p>
            <div className="flex gap-3 justify-center">
              <Link href="/ads">
                <Button
                  variant="outline"
                  className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                >
                  Browse Ad Library
                </Button>
              </Link>
              <Link href="/ads/templates">
                <Button className="bg-blue-gradient-hover text-white shadow-lg">
                  My Templates
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
