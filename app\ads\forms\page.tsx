"use client"

import { useState } from "react"
import { Search, Plus, Edit, Co<PERSON>, Trash2, Eye, Settings, Users, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Navigation } from "@/components/navigation"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { CreateLeadFormModal } from "@/components/create-lead-form-modal"

const leadForms = [
  {
    id: 1,
    name: "Free Pressure Washing Quote",
    status: "Active",
    leads: 127,
    conversionRate: 18.5,
    lastModified: "2025-07-18",
    campaigns: 3,
    fields: ["Name", "Email", "Phone", "Property Type", "Service Needed"],
    description: "Standard quote request form for residential pressure washing services",
  },
  {
    id: 2,
    name: "Commercial Fleet Washing Inquiry",
    status: "Active",
    leads: 23,
    conversionRate: 31.2,
    lastModified: "2025-07-15",
    campaigns: 1,
    fields: ["Company Name", "Contact Name", "Email", "Phone", "Fleet Size", "Location"],
    description: "Specialized form for commercial fleet washing inquiries",
  },
  {
    id: 3,
    name: "Roof Cleaning Consultation",
    status: "Draft",
    leads: 0,
    conversionRate: 0,
    lastModified: "2025-07-10",
    campaigns: 0,
    fields: ["Name", "Email", "Phone", "Roof Type", "Property Age", "Previous Cleaning"],
    description: "Detailed form for roof cleaning consultations and estimates",
  },
  {
    id: 4,
    name: "Spring Cleaning Package",
    status: "Paused",
    leads: 89,
    conversionRate: 22.1,
    lastModified: "2025-07-05",
    campaigns: 2,
    fields: ["Name", "Email", "Phone", "Property Size", "Services Interested"],
    description: "Seasonal promotion form for spring cleaning packages",
  },
]

export default function LeadFormsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedForm, setSelectedForm] = useState<any>(null)

  const statuses = ["all", "Active", "Draft", "Paused", "Archived"]

  const filteredForms = leadForms.filter((form) => {
    const matchesSearch = form.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === "all" || form.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Active</Badge>
      case "Draft":
        return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Draft</Badge>
      case "Paused":
        return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Paused</Badge>
      case "Archived":
        return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Archived</Badge>
      default:
        return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Unknown</Badge>
    }
  }

  const totalLeads = leadForms.reduce((sum, form) => sum + form.leads, 0)
  const avgConversionRate = leadForms.reduce((sum, form) => sum + form.conversionRate, 0) / leadForms.length

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="lead-forms" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Lead Forms</h1>
            <p className="text-neutral-400 mt-1 text-sm">
              Create and manage lead capture forms for your Facebook campaigns
            </p>
          </div>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center gap-2 bg-blue-gradient-hover text-white shadow-lg"
          >
            <Plus className="h-4 w-4" />
            Create New Form
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Forms</p>
                  <p className="text-2xl font-medium text-white">{leadForms.length}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Settings className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Active Forms</p>
                  <p className="text-2xl font-medium text-white">
                    {leadForms.filter((f) => f.status === "Active").length}
                  </p>
                </div>
                <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Active</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Leads</p>
                  <p className="text-2xl font-medium text-white">{totalLeads}</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Avg. Conversion</p>
                  <p className="text-2xl font-medium text-white">{avgConversionRate.toFixed(1)}%</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-neutral-900 border-neutral-800 mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search forms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                />
              </div>

              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-48 bg-neutral-800 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-neutral-900 border-neutral-800">
                  {statuses.map((status) => (
                    <SelectItem
                      key={status}
                      value={status}
                      className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                    >
                      {status === "all" ? "All Status" : status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Forms Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredForms.map((form) => (
            <Card key={form.id} className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-base font-medium text-white">{form.name}</CardTitle>
                    <p className="text-sm text-neutral-400 mt-1">{form.description}</p>
                  </div>
                  {getStatusBadge(form.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Performance Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-neutral-800 rounded-lg">
                    <div className="text-xl font-medium text-white">{form.leads}</div>
                    <p className="text-xs text-neutral-400">Total Leads</p>
                  </div>
                  <div className="text-center p-3 bg-neutral-800 rounded-lg">
                    <div className="text-xl font-medium text-white">{form.conversionRate}%</div>
                    <p className="text-xs text-neutral-400">Conversion Rate</p>
                  </div>
                </div>

                {/* Form Fields */}
                <div>
                  <p className="text-sm font-medium text-white mb-2">Form Fields ({form.fields.length})</p>
                  <div className="flex flex-wrap gap-1">
                    {form.fields.slice(0, 3).map((field) => (
                      <Badge key={field} className="text-xs bg-neutral-800 text-neutral-400 border-neutral-700">
                        {field}
                      </Badge>
                    ))}
                    {form.fields.length > 3 && (
                      <Badge className="text-xs bg-neutral-800 text-neutral-400 border-neutral-700">
                        +{form.fields.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Meta Info */}
                <div className="flex items-center justify-between text-xs text-neutral-500 pt-2 border-t border-neutral-800">
                  <span>Modified: {form.lastModified}</span>
                  <span>{form.campaigns} campaigns</span>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                    onClick={() => setSelectedForm(form)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
                      >
                        •••
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-neutral-900 border-neutral-800">
                      <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Form
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                        <Users className="h-4 w-4 mr-2" />
                        View Leads
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-400 hover:text-red-300 hover:bg-neutral-800">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredForms.length === 0 && (
          <div className="text-center py-12">
            <Settings className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No forms found</h3>
            <p className="text-neutral-400 mb-4">
              {searchTerm || selectedStatus !== "all"
                ? "Try adjusting your search or filters"
                : "Create your first lead form to start capturing leads from Facebook campaigns"}
            </p>
            <Button onClick={() => setShowCreateModal(true)} className="bg-blue-gradient-hover text-white shadow-lg">
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Form
            </Button>
          </div>
        )}
      </main>

      <CreateLeadFormModal open={showCreateModal} onOpenChange={setShowCreateModal} />
    </div>
  )
}
