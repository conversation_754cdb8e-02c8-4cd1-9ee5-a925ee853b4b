"use client"

import { useEffect, useRef, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Environment } from "@react-three/drei"
import Orb, { type Speaker } from "./orb"
import { RealtimeAudio } from "./realtime-audio"

interface AIOrbWidgetProps {
  /**
   * The current speaker state (0: thinking, 1: user, 2: agent).
   * If provided, overrides internal simulation.
   */
  speaker?: Speaker
  /**
   * The current audio volume (0-1).
   * If provided, overrides internal microphone input.
   */
  volume?: number
  /**
   * Enables or disables internal microphone input for volume detection.
   * Defaults to true if `volume` prop is not provided.
   */
  enableMicInput?: boolean
  /**
   * Enables or disables internal speaker state simulation.
   * Defaults to true if `speaker` prop is not provided.
   */
  simulateSpeaker?: boolean
}

export default function AIOrbWidget({
  speaker: externalSpeaker,
  volume: externalVolume,
  enableMicInput,
  simulateSpeaker,
}: AIOrbWidgetProps) {
  // Internal state for speaker and volume if not externally controlled
  const [internalSpeaker, setInternalSpeaker] = useState<Speaker>(0)
  const [internalVolume, setInternalVolume] = useState(0)
  const audioRef = useRef<RealtimeAudio>(new RealtimeAudio())
  const [isAudioEnabled, setIsAudioEnabled] = useState(false)

  // Determine which speaker/volume to use (external props take precedence)
  const currentSpeaker = externalSpeaker !== undefined ? externalSpeaker : internalSpeaker
  const currentVolume = externalVolume !== undefined ? externalVolume : internalVolume

  // Determine if internal mic input should be active
  const useInternalMic = enableMicInput !== undefined ? enableMicInput : externalVolume === undefined

  // Determine if internal speaker simulation should be active
  const useInternalSpeakerSimulation = simulateSpeaker !== undefined ? simulateSpeaker : externalSpeaker === undefined

  // --- Mic loop (only if internal mic is enabled) ---
  useEffect(() => {
    if (!useInternalMic) return

    if (!isAudioEnabled) return // Wait for user permission

    let animationFrameHandle: number
    const initAudio = async () => {
      await audioRef.current.init()
      const tick = () => {
        setInternalVolume(audioRef.current.getVolume())
        animationFrameHandle = requestAnimationFrame(tick)
      }
      tick()
    }

    initAudio()

    return () => {
      if (animationFrameHandle) cancelAnimationFrame(animationFrameHandle)
      audioRef.current.close()
    }
  }, [useInternalMic, isAudioEnabled])

  // --- Simulate speaker switching (only if internal simulation is enabled) ---
  useEffect(() => {
    if (!useInternalSpeakerSimulation) return

    const intervalId = setInterval(() => {
      setInternalSpeaker((prev) => ((prev + 1) % 3) as Speaker)
    }, 5000) // demo toggle every 5s
    return () => clearInterval(intervalId)
  }, [useInternalSpeakerSimulation])

  const handleEnableAudio = async () => {
    setIsAudioEnabled(true)
  }

  return (
    <div className="w-full h-[500px] bg-black relative rounded-lg overflow-hidden">
      <Canvas camera={{ position: [0, 0, 5] }}>
        <Environment preset="studio" />
        <ambientLight intensity={0.2} />
        <Orb speaker={currentSpeaker} volume={currentVolume} />
        <OrbitControls enableZoom={false} enablePan={false} />
      </Canvas>

      {/* Audio permission button - only show if internal mic input is active and not yet enabled */}
      {useInternalMic && !isAudioEnabled && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="text-center">
            <button
              onClick={handleEnableAudio}
              className="px-8 py-4 bg-gradient-to-r from-[#AD1FBF] to-[#7176D9] text-white rounded-full hover:from-[#7176D9] hover:to-[#27CDF2] transition-all duration-300 shadow-lg"
            >
              Enable Microphone
            </button>
            <p className="text-white mt-4 text-sm">Click to enable microphone for sound-responsive orb</p>
          </div>
        </div>
      )}

      {/* Status indicator */}
      <div className="absolute top-6 left-6 text-white">
        <div className="flex items-center space-x-3 bg-black bg-opacity-60 backdrop-blur-sm rounded-full px-4 py-2 border border-white border-opacity-20">
          <div
            className={`w-3 h-3 rounded-full ${
              currentSpeaker === 0
                ? "bg-gradient-to-r from-[#7176D9] to-[#57AAF2]" // Thinking: Purple to Light Blue
                : currentSpeaker === 1
                  ? "bg-gradient-to-r from-[#AD1FBF] to-[#7176D9]" // User: Magenta to Purple
                  : "bg-gradient-to-r from-[#57AAF2] to-[#27CDF2]" // Agent: Light Blue to Cyan
            }`}
          />
          <span className="text-sm font-medium">
            {currentSpeaker === 0 ? "Thinking..." : currentSpeaker === 1 ? "User Speaking" : "Agent Speaking"}
          </span>
        </div>
        <div className="mt-2 text-xs text-gray-300 bg-black bg-opacity-40 backdrop-blur-sm rounded-full px-3 py-1">
          Volume: {Math.round(currentVolume * 100)}%
        </div>
      </div>
    </div>
  )
}
