# AI VoiceConnect Design Rules

This document outlines the core design principles and specific UI guidelines for the application. Adhering to these rules ensures a consistent, modern, and user-friendly experience across all features.

## 1. Color Palette

Our primary color palette is based on a dark theme with a vibrant blue accent.

-   **Background**: `hsl(0 0% 0%)` (Black)
-   **Foreground Text**: `hsl(0 0% 98%)` (Near White)
-   **Card/Popover Background**: `hsl(0 0% 3.9%)` (Dark Gray)
-   **Muted Text/Icons**: `hsl(0 0% 63.9%)` (Light Gray)
-   **Primary Accent Blue**: `hsl(217 91% 60%)`
    -   Used for active states, primary actions, and key visual elements.
-   **Blue Gradient Start**: `hsl(217 91% 70%)`
-   **Blue Gradient End**: `hsl(217 91% 45%)`
-   **Border/Input**: `hsl(0 0% 14.9%)` (Darker Gray)
-   **Destructive (Red)**: `hsl(0 62.8% 30.6%)` (for errors, delete actions)
-   **Warning (Yellow)**: `hsl(40 80% 50%)` (for warnings, paused states)
-   **Success (Green)**: `hsl(142 76% 36%)` (for success messages, completed states)

## 2. Gradients

Gradients are a key visual element, primarily using the defined blue gradient.

-   **`bg-blue-gradient`**: Applied to prominent background elements, icons, and selected navigation items.
    -   `background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));`
-   **`bg-blue-gradient-hover`**: Used for interactive elements (buttons) that should have a distinct hover effect.
    -   Includes a `transform: translateY(-1px)` and `box-shadow` for a subtle lift and glow on hover.

## 3. Typography

We use the Geist font stack for a clean and modern look.

-   **Font Family**: `Geist`, -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
-   **Headings**: Use `font-medium` or `font-semibold` for titles (`h1`, `h2`, `h3`).
-   **Body Text**: Use `text-neutral-400` for general descriptive text.
-   **Important Text**: Use `text-white` for key information and labels.

## 4. Component Styling

### 4.1. Cards

-   **Default**: `bg-neutral-900`, `border-neutral-800`.
-   **Hover Effect**: Use `card-hover-blue` class for interactive cards.
    -   `transition: all 0.3s ease;`
    -   `border-color: hsl(217 91% 60% / 0.4);`
    -   `box-shadow: 0 8px 25px -5px hsl(217 91% 60% / 0.1);`
-   **Inner Cards (e.g., within modals)**: `bg-neutral-800`, `border-neutral-700`.

### 4.2. Buttons

-   **Primary Action Buttons**: Use `bg-blue-gradient-hover` for main calls to action.
    -   `text-white`, `shadow-lg`.
-   **Outline Buttons**: Use `variant="outline"` with `border-blue-500/20`, `text-blue-400`, `bg-transparent`.
    -   Hover: `hover:bg-blue-500/10`, `hover:text-blue-300`, `hover:border-blue-500/40`.
-   **Ghost Buttons**: Use `variant="ghost"` with `text-blue-400`.
    -   Hover: `hover:text-blue-300`, `hover:bg-blue-500/10`.
-   **Destructive Buttons**: Use `text-red-400` for delete/remove actions.
    -   Hover: `hover:text-red-300`, `hover:bg-neutral-800`.

### 4.3. Inputs & Textareas

-   **Default**: `bg-neutral-900`, `border-neutral-800`, `text-white`, `placeholder:text-neutral-500`.
-   **Inner Inputs (e.g., within cards/modals)**: `bg-neutral-800`, `border-neutral-700`.

### 4.4. Selects

-   **Trigger**: `bg-neutral-900`, `border-blue-500/20`, `text-white`.
    -   Hover: `hover:border-blue-500/40`, `transition-colors`.
-   **Content/Items**: `bg-neutral-900`, `border-neutral-800`.
    -   Items: `text-neutral-300`, `hover:text-white`, `hover:bg-blue-500/10`.

### 4.5. Badges

-   **Active/Success**: `bg-blue-500/10`, `text-blue-400`, `border-blue-500/20`.
-   **Warning/Paused**: `bg-yellow-500/10`, `text-yellow-400`, `border-yellow-500/20`.
-   **Neutral/Default**: `bg-neutral-500/10`, `text-neutral-400`, `border-neutral-500/20`.
-   **Custom/Secondary**: `bg-purple-500/10`, `text-purple-400`, `border-purple-500/20`.
-   **Inner/Tag Badges**: `bg-neutral-800`, `text-neutral-400`, `border-neutral-700`.

### 4.6. Dialogs (Modals)

-   **Content**: `bg-neutral-900`, `border-neutral-800`.
-   **Header/Footer Borders**: `border-neutral-800`.

### 4.7. Tabs

-   **List**: `bg-neutral-900`, `border-blue-500/20`.
-   **Trigger**: `text-neutral-400`.
    -   Active: `data-[state=active]:bg-blue-500/10`, `data-[state=active]:text-blue-400`.
    -   Hover: `hover:bg-blue-500/5`.

## 5. Icons

-   All icons should be sourced from `lucide-react`.
-   Default icon color should be `text-neutral-400` or `text-neutral-500`, unless part of an accent element (e.g., inside a blue gradient background).

## 6. Shadows and Glows

-   **`glow-blue`**: Applied to elements with `bg-blue-gradient` for a subtle glow effect.
    -   `box-shadow: 0 0 20px hsl(217 91% 60% / 0.3);`
-   **`glow-blue-strong`**: For more prominent glow effects.
    -   `box-shadow: 0 0 30px hsl(217 91% 60% / 0.5);`

## 7. Responsiveness

All components and layouts must be designed with responsiveness in mind, adapting gracefully to various screen sizes (mobile, tablet, desktop). Use Tailwind's responsive utilities (`sm:`, `md:`, `lg:`, `xl:`).

## 8. Accessibility

Accessibility best practices are paramount.

-   Use semantic HTML elements.
-   Ensure proper ARIA roles and attributes where necessary.
-   Provide `alt` text for all meaningful images.
-   Use `sr-only` for screen reader-only text when visual text is not needed but context is.
-   Ensure sufficient color contrast.

By following these design rules, we can maintain a cohesive and high-quality user experience throughout the application.
