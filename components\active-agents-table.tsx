import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Play, Pause, BarChart3 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const agents = [
  {
    id: 1,
    name: "Main Office Line (Incoming)",
    type: "Inbound",
    status: "active",
    appointmentsToday: 3,
  },
  {
    id: 2,
    name: "Speed-to-Lead (New Leads)",
    type: "Outbound",
    status: "active",
    appointmentsToday: 5,
  },
  {
    id: 3,
    name: "Q2 Reactivation Campaign",
    type: "Campaign",
    status: "paused",
    appointmentsToday: 0,
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border-blue-500/20">Active</Badge>
    case "paused":
      return (
        <Badge className="bg-yellow-500/10 text-yellow-400 hover:bg-yellow-500/20 border-yellow-500/20">Paused</Badge>
      )
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Inactive</Badge>
  }
}

export function ActiveAgentsTable() {
  return (
    <div className="space-y-3">
      {agents.map((agent) => (
        <div
          key={agent.id}
          className="flex items-center justify-between p-4 border border-neutral-800 rounded-lg hover:bg-neutral-800/50 transition-colors"
        >
          <div className="flex-1">
            <h4 className="font-medium text-white text-sm">{agent.name}</h4>
            <div className="flex items-center space-x-4 mt-1">
              <span className="text-xs text-neutral-500">Type: {agent.type}</span>
              {getStatusBadge(agent.status)}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-lg font-medium text-white">{agent.appointmentsToday}</div>
              <div className="text-xs text-neutral-500">Appts Today</div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-neutral-700 text-neutral-400 bg-transparent hover:bg-neutral-800 hover:text-white"
                >
                  Details
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-neutral-900 border-neutral-800">
                <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </DropdownMenuItem>
                <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
                  {agent.status === "active" ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause Agent
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Activate Agent
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-neutral-800">
                  Edit Settings
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      ))}
    </div>
  )
}
