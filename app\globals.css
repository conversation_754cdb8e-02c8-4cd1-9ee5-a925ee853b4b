@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 217 91% 60%;
    --radius: 0.5rem;

    /* Custom blue gradient variables */
    --blue-gradient-start: 217 91% 70%;
    --blue-gradient-end: 217 91% 45%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* <PERSON>eist Font Stack */
body {
  font-family: "Geist", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell",
    "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(0 0% 3.9%);
}

::-webkit-scrollbar-thumb {
  background: hsl(0 0% 14.9%);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(0 0% 20%);
}

/* Blue gradient utilities */
.bg-blue-gradient {
  background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
}

.bg-blue-gradient-hover {
  background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
  transition: all 0.2s ease;
}

.bg-blue-gradient-hover:hover {
  background: linear-gradient(135deg, hsl(217 91% 75%), hsl(217 91% 50%));
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

/* Accent blue utilities */
.accent-blue {
  color: hsl(217 91% 60%);
}

.bg-accent-blue {
  background-color: hsl(217 91% 60% / 0.1);
}

.border-accent-blue {
  border-color: hsl(217 91% 60% / 0.2);
}

/* Glow effects */
.glow-blue {
  box-shadow: 0 0 20px hsl(217 91% 60% / 0.3);
}

.glow-blue-strong {
  box-shadow: 0 0 30px hsl(217 91% 60% / 0.5);
}

/* Card hover effects */
.card-hover-blue {
  transition: all 0.3s ease;
}

.card-hover-blue:hover {
  border-color: hsl(217 91% 60% / 0.4);
  box-shadow: 0 8px 25px -5px hsl(217 91% 60% / 0.1);
}
