"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, Download, Phone, Calendar, Clock, User, Filter, Play, FileText } from "lucide-react"
import { Navigation } from "@/components/navigation"

const callLogs = [
  {
    id: 1,
    timestamp: "2025-07-19 14:32:15",
    leadName: "<PERSON>",
    phoneNumber: "(*************",
    agent: "Main Office Line",
    duration: "3:45",
    outcome: "Booked",
    status: "success",
  },
  {
    id: 2,
    timestamp: "2025-07-19 14:28:22",
    leadName: "<PERSON>",
    phoneNumber: "(*************",
    agent: "Speed-to-Lead Agent",
    duration: "2:12",
    outcome: "No Answer",
    status: "no-answer",
  },
  {
    id: 3,
    timestamp: "2025-07-19 14:15:33",
    leadName: "<PERSON>",
    phoneNumber: "(*************",
    agent: "Trash to Cash Agent",
    duration: "4:22",
    outcome: "Interested",
    status: "qualified",
  },
  {
    id: 4,
    timestamp: "2025-07-19 13:58:41",
    leadName: "Lisa Brown",
    phoneNumber: "(*************",
    agent: "Main Office Line",
    duration: "1:33",
    outcome: "Disqualified",
    status: "disqualified",
  },
  {
    id: 5,
    timestamp: "2025-07-19 13:45:12",
    leadName: "David Lee",
    phoneNumber: "(*************",
    agent: "Speed-to-Lead Agent",
    duration: "5:18",
    outcome: "Transferred",
    status: "transferred",
  },
]

const getOutcomeBadge = (outcome: string, status: string) => {
  switch (status) {
    case "success":
      return <Badge className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border-blue-500/20">{outcome}</Badge>
    case "qualified":
      return <Badge className="bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border-blue-500/20">{outcome}</Badge>
    case "transferred":
      return (
        <Badge className="bg-purple-500/10 text-purple-400 hover:bg-purple-500/20 border-purple-500/20">
          {outcome}
        </Badge>
      )
    case "no-answer":
      return (
        <Badge className="bg-yellow-500/10 text-yellow-400 hover:bg-yellow-500/20 border-yellow-500/20">
          {outcome}
        </Badge>
      )
    case "disqualified":
      return <Badge className="bg-red-500/10 text-red-400 hover:bg-red-500/20 border-red-500/20">{outcome}</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{outcome}</Badge>
  }
}

export default function AnalyticsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [agentFilter, setAgentFilter] = useState("all")
  const [outcomeFilter, setOutcomeFilter] = useState("all")

  const filteredLogs = callLogs.filter((log) => {
    const matchesSearch =
      log.leadName.toLowerCase().includes(searchTerm.toLowerCase()) || log.phoneNumber.includes(searchTerm)
    const matchesAgent = agentFilter === "all" || log.agent === agentFilter
    const matchesOutcome = outcomeFilter === "all" || log.outcome.toLowerCase() === outcomeFilter.toLowerCase()
    return matchesSearch && matchesAgent && matchesOutcome
  })

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="analytics" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Analytics</h1>
            <p className="text-neutral-400 mt-1 text-sm">Detailed call logs and performance reports</p>
          </div>
          <Button className="flex items-center gap-2 bg-blue-gradient-hover text-white shadow-lg">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Total Calls Today</p>
                  <p className="text-2xl font-medium text-white">127</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Phone className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Appointments</p>
                  <p className="text-2xl font-medium text-white">23</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Avg Call Duration</p>
                  <p className="text-2xl font-medium text-white">3:24</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <Clock className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-400">Conversion Rate</p>
                  <p className="text-2xl font-medium text-white">18.1%</p>
                </div>
                <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                  <User className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call Log */}
        <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <FileText className="h-4 w-4" />
                Call Log
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                >
                  <Filter className="h-4 w-4 mr-1" />
                  Filters
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
                >
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500 h-4 w-4" />
                <Input
                  placeholder="Search by name or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
                />
              </div>
              <Select value={agentFilter} onValueChange={setAgentFilter}>
                <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                  <SelectValue placeholder="Filter by agent" />
                </SelectTrigger>
                <SelectContent className="bg-neutral-900 border-neutral-800">
                  <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    All Agents
                  </SelectItem>
                  <SelectItem
                    value="Main Office Line"
                    className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                  >
                    Main Office Line
                  </SelectItem>
                  <SelectItem
                    value="Speed-to-Lead Agent"
                    className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                  >
                    Speed-to-Lead Agent
                  </SelectItem>
                  <SelectItem
                    value="Trash to Cash Agent"
                    className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                  >
                    Trash to Cash Agent
                  </SelectItem>
                </SelectContent>
              </Select>
              <Select value={outcomeFilter} onValueChange={setOutcomeFilter}>
                <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                  <SelectValue placeholder="Filter by outcome" />
                </SelectTrigger>
                <SelectContent className="bg-neutral-900 border-neutral-800">
                  <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    All Outcomes
                  </SelectItem>
                  <SelectItem value="booked" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    Booked
                  </SelectItem>
                  <SelectItem value="interested" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    Interested
                  </SelectItem>
                  <SelectItem value="transferred" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    Transferred
                  </SelectItem>
                  <SelectItem value="no answer" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    No Answer
                  </SelectItem>
                  <SelectItem value="disqualified" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                    Disqualified
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Call Log Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-neutral-800">
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Timestamp</th>
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Lead Name</th>
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Phone</th>
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Agent</th>
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Duration</th>
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Outcome</th>
                    <th className="text-left py-3 px-4 font-medium text-neutral-400 text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredLogs.map((log) => (
                    <tr key={log.id} className="border-b border-neutral-800 hover:bg-neutral-800/50">
                      <td className="py-3 px-4 text-sm text-white">{new Date(log.timestamp).toLocaleString()}</td>
                      <td className="py-3 px-4 text-sm font-medium text-white">{log.leadName}</td>
                      <td className="py-3 px-4 text-sm text-neutral-400">{log.phoneNumber}</td>
                      <td className="py-3 px-4 text-sm text-neutral-400">{log.agent}</td>
                      <td className="py-3 px-4 text-sm text-neutral-400">{log.duration}</td>
                      <td className="py-3 px-4">{getOutcomeBadge(log.outcome, log.status)}</td>
                      <td className="py-3 px-4">
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredLogs.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-neutral-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No calls found</h3>
                <p className="text-neutral-400">
                  {searchTerm || agentFilter !== "all" || outcomeFilter !== "all"
                    ? "Try adjusting your search or filters"
                    : "Call logs will appear here once your agents start making calls"}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
