"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Phone, TrendingUp, Users, Activity, BarChart3 } from "lucide-react"
import { Navigation } from "@/components/navigation"
import { RecentActivity } from "@/components/recent-activity"
import { ActiveAgentsTable } from "@/components/active-agents-table"

export default function Dashboard() {
  const [dateRange, setDateRange] = useState("last-30-days")

  const kpiData = {
    appointmentsBooked: { value: 142, trend: "+12%" },
    callsConnected: { value: 1109, trend: "+8%" },
    positiveOutcomes: { value: 78, trend: "+5%" },
  }

  return (
    <div className="min-h-screen bg-black">
      <Navigation activeTab="dashboard" />

      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-medium text-white">Dashboard</h1>
            <p className="text-neutral-400 mt-1 text-sm">Monitor your AI agents and campaign performance</p>
          </div>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-neutral-900 border-neutral-800">
              <SelectItem value="today" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Today
              </SelectItem>
              <SelectItem value="last-7-days" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Last 7 Days
              </SelectItem>
              <SelectItem value="last-30-days" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Last 30 Days
              </SelectItem>
              <SelectItem value="custom" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                Custom Range
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 bg-blue-gradient opacity-10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-neutral-400">Appointments Booked</CardTitle>
              <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                <Calendar className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-medium text-white">{kpiData.appointmentsBooked.value}</div>
              <div className="flex items-center mt-2">
                <TrendingUp className="h-3 w-3 text-blue-400 mr-1" />
                <span className="text-xs text-blue-400 font-medium">{kpiData.appointmentsBooked.trend}</span>
                <span className="text-xs text-neutral-500 ml-1">vs last period</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 bg-blue-gradient opacity-10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-neutral-400">Calls Connected</CardTitle>
              <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                <Phone className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-medium text-white">{kpiData.callsConnected.value.toLocaleString()}</div>
              <div className="flex items-center mt-2">
                <TrendingUp className="h-3 w-3 text-blue-400 mr-1" />
                <span className="text-xs text-blue-400 font-medium">{kpiData.callsConnected.trend}</span>
                <span className="text-xs text-neutral-500 ml-1">vs last period</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue relative overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 bg-blue-gradient opacity-10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-neutral-400">Positive Outcomes</CardTitle>
              <div className="p-2 bg-blue-gradient rounded-lg glow-blue">
                <Users className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-medium text-white">{kpiData.positiveOutcomes.value}%</div>
              <div className="flex items-center mt-2">
                <TrendingUp className="h-3 w-3 text-blue-400 mr-1" />
                <span className="text-xs text-blue-400 font-medium">{kpiData.positiveOutcomes.trend}</span>
                <span className="text-xs text-neutral-500 ml-1">vs last period</span>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <Activity className="h-4 w-4 text-white" />
                </div>
                Recent Activity
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40"
              >
                View All
              </Button>
            </CardHeader>
            <CardContent>
              <RecentActivity />
            </CardContent>
          </Card>

          {/* Active Agents & Campaigns */}
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-white">
                <div className="p-2 bg-blue-gradient rounded-lg">
                  <BarChart3 className="h-4 w-4 text-white" />
                </div>
                Active Agents & Campaigns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ActiveAgentsTable />
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
